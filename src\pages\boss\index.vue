<template>
  <view class="page-wrap">
    <view class="header-wrap">
      <image
        :src="$getStaticImg('img/yfk/login/yfk_shd_bg-fk.png')"
        mode=""
        class="img"
      />
      <text class="title">首页</text>
      <view class="nums-container">
        <view
          class="label-item"
          :style="{
            backgroundImage:
              'url(' + $getStaticImg('img/yfk/login/header_bk_fk.png') + ')',
          }"
        >
          <view class="flex-b">
            <view class="flex-c">
              <image
                :src="$getStaticImg('img/yfk/login/yfk_shd_ico1.png')"
                mode=""
              />
              <text>{{ dpmc || '' }}</text>
            </view>
            <image
              :src="$getStaticImg('img/yfk/login/logout_btn.png')"
              mode="aspectFill"
              style="width: 104rpx; height: 30rpx"
              @click="show = true"
            />
          </view>
        </view>
        <view class="nums-wrap">
          <view class="nums-item">
            <view class="item">
              <label>{{ num1 || 0 }}</label>
              <text>今日核销金额</text>
            </view>
            <view class="item">
              <label>{{ num2 || 0 }}</label>
              <text>本月核销金额</text>
            </view>
          </view>
          <view class="divider"></view>
          <view class="checkTotal" @click="goStatistics">
            <view>查看统计</view>
            <u-icon name="arrow-right" color="#aaa"></u-icon>
          </view>
        </view>
      </view>
    </view>
    <view class="service-wrap">
      <text class="title">商家服务</text>
      <view class="options-container">
        <view class="options-item" @tap="toQHX">
          <image
            :src="$getStaticImg('img/yfk/login/yfk_shd_ico2.png')"
            mode=""
            class="pro-ico"
          />
          <text class="item-name">去核销</text>
          <image
            class="next-ico"
            :src="$getStaticImg('img/yfk/login/yfk_shd_ico4.png')"
          />
        </view>
        <view class="options-item" @tap="toHXJL">
          <image
            class="pro-ico"
            :src="$getStaticImg('img/yfk/login/yfk_shd_ico3.png')"
          />
          <text class="item-name">核销记录</text>
          <image
            class="next-ico"
            :src="$getStaticImg('img/yfk/login/yfk_shd_ico4.png')"
          />
        </view>
        <view class="options-item" @tap="toHXM">
          <image
            class="pro-ico"
            :src="$getStaticImg('img/yfk/login/boss_hxm.png')"
          />
          <text class="item-name">核销码</text>
          <image
            class="next-ico"
            :src="$getStaticImg('img/yfk/login/yfk_shd_ico4.png')"
          />
        </view>
        <!-- <view class="options-item" @tap="toYHQHX">
          <image
            class="pro-ico"
            :src="$getStaticImg('images/yfk/login/yfk_hx.png')"
          />
          <text class="item-name">优惠券核销</text>
          <image
            class="next-ico"
            :src="$getStaticImg('img/yfk/login/yfk_shd_ico4.png')"
          />
        </view> -->
      </view>
    </view>
    <!-- <button class="exit-btn" @tap="exit">退出登录</button> -->
    <u-modal
      :show="show"
      content="是否确认退出？"
      :showCancelButton="true"
      @confirm="exit"
      @cancel="show = false"
    ></u-modal>
  </view>
</template>

<script>
import { mapMutations } from 'vuex'
import { getJeCard, shopHx, shopHxje } from '@/services/yfk/boss/hxjl.js'
import { shopHx2, shopHx3 } from '@/services/yfk/boss/hxjl.js'
export default {
  data() {
    return {
      num1: 0,
      num2: 0,
      dpmc: '',
      show: false,
    }
  },
  computed: {},
  methods: {
    ...mapMutations(['logout']),
    //跳转至去核销
    toQHX() {
      this.scanCodeHandler()
    },
    // 跳转至核销记录
    toHXJL() {
      uni.navigateTo({
        url: '/pages/boss/hxjl',
      })
    },
    // 跳转至优惠券核销
    toYHQHX() {
      this.scanCodeHandler()
    },
    //跳转核销码
    toHXM() {
      uni.navigateTo({
        url: '/pages/boss/hxm',
      })
    },
    // 退出登录
    exit() {
      this.logout()
      uni.redirectTo({
        url: '/pages/yfk_login/login',
      })
    },
    // 扫码按钮点击回调
    scanCodeHandler() {
      const that = this
      // #ifdef H5

      window.parent.postMessage(
        {
          cmd: 'scanCodes',
        },
        '*'
      )
      window.addEventListener('message', (event) => {
        if (event.data.cmd == 'getScanCodes') {
          if (event.data.value) {
            console.log(event.data.value, '扫码结果')
            if (event.data.value.errMsg === 'scanCode:ok') {
              if (event.data.value.result) {
                let resdata = JSON.parse(event.data.value.result)
                if (resdata.type == 4) {
                  //兑换券扫码核销
                  uni.showLoading({ mask: true })
                  shopHx2({
                    type: 1,
                    code: resdata.code,
                  })
                    .then((res) => {
                      uni.hideLoading()
                      if (res.code === 200) {
                        uni.redirectTo({
                          url:
                            '/pages/boss/hxjg-xj?order=' +
                            encodeURIComponent(JSON.stringify(res.data)),
                        })
                      } else {
                        uni.showToast({
                          title: res.msg,
                          position: 'bottom',
                          duration: 3000,
                          icon: 'none',
                        })
                      }
                    })
                    .catch(() => {
                      uni.showToast({
                        title: res.msg,
                        position: 'bottom',
                        duration: 3000,
                        icon: 'none',
                      })
                      uni.hideLoading()
                    })
                } else if (resdata.type == 6) {
                  //我的订单扫码核销
                  uni.showLoading({ mask: true })
                  shopHx3({
                    type: 1,
                    code: resdata.code,
                  })
                    .then((res) => {
                      uni.hideLoading()
                      if (res.code === 200) {
                        uni.redirectTo({
                          url:
                            '/pages/boss/hxjg-xj?order=' +
                            encodeURIComponent(JSON.stringify(res.data)),
                        })
                      } else {
                        uni.showToast({
                          title: res.msg,
                          position: 'bottom',
                          duration: 3000,
                          icon: 'none',
                        })
                      }
                    })
                    .catch(() => {
                      uni.showToast({
                        title: res.msg,
                        position: 'bottom',
                        duration: 3000,
                        icon: 'none',
                      })
                      uni.hideLoading()
                    })
                } else {
                  uni.navigateTo({
                    url:
                      '/pages/boss/smhx?codedata=' +
                      encodeURIComponent(
                        JSON.stringify(event.data.value.result)
                      ),
                  })
                }
              }
            }
          }
        }
      })

      // #endif
      // #ifdef MP-WEIXIN
      // 调起条码扫描
      uni.scanCode({
        onlyFromCamera: false, // 是否只能从相机扫码，不允许从相册选择图片
        scanType: ['qrCode'], // 扫码类型 qrCode二维码
        success: (res) => {
          console.log(res, '扫码结果')
          if (res.errMsg === 'scanCode:ok') {
            if (res.result) {
              let resdata = JSON.parse(res.result)
              if (resdata.type == 4) {
                //兑换券扫码核销
                uni.showLoading({ mask: true })
                shopHx2({
                  type: 1,
                  code: resdata.code,
                })
                  .then((res) => {
                    uni.hideLoading()
                    if (res.code === 200) {
                      uni.redirectTo({
                        url:
                          '/pages/boss/hxjg-xj?order=' +
                          encodeURIComponent(JSON.stringify(res.data)),
                      })
                    } else {
                      uni.showToast({
                        title: res.msg,
                        position: 'bottom',
                        duration: 3000,
                        icon: 'none',
                      })
                    }
                  })
                  .catch(() => {
                    uni.showToast({
                      title: res.msg,
                      position: 'bottom',
                      duration: 3000,
                      icon: 'none',
                    })
                    uni.hideLoading()
                  })
              } else if (resdata.type == 6) {
                //我的订单扫码核销
                uni.showLoading({ mask: true })
                shopHx3({
                  type: 1,
                  code: resdata.code,
                })
                  .then((res) => {
                    uni.hideLoading()
                    if (res.code === 200) {
                      uni.redirectTo({
                        url:
                          '/pages/boss/hxjg-xj?order=' +
                          encodeURIComponent(JSON.stringify(res.data)),
                      })
                    } else {
                      uni.showToast({
                        title: res.msg,
                        position: 'bottom',
                        duration: 3000,
                        icon: 'none',
                      })
                    }
                  })
                  .catch(() => {
                    uni.showToast({
                      title: res.msg,
                      position: 'bottom',
                      duration: 3000,
                      icon: 'none',
                    })
                    uni.hideLoading()
                  })
              } else {
                //type==1是扫码支付二维码
                if (resdata.hxType == 4) {
                  //爱生活
                  uni.navigateTo({
                    url:
                      '/pages/boss/smhx?codedata=' +
                      encodeURIComponent(JSON.stringify(resdata)),
                  })
                } else {
                  setTimeout(() => {
                    uni.showToast({
                      title: '小程序错误，请使用“爱金哇”小程序扫码',
                      position: 'bottom',
                      duration: 3000,
                      icon: 'none',
                    })
                  }, 1000)
                }
              }
            }
          }
        },
        fail: (err) => {
          console.log(`错误：${err}`)
        },
      })
      // #endif
    },
    // 初始化页面时显示所需的数据
    init() {
      shopHxje()
        .then((res) => {
          if (res.code === 200) {
            this.num1 = res.data.drhxje
            this.num2 = res.data.dyhxje
            this.dpmc = res.data.dpmc
          }
        })
        .catch(() => {
          console.log('2222', res)
        })
    },
    goStatistics() {
      uni.navigateTo({
        url: '/pages/boss/statistics',
      })
    },
  },
  // 下拉刷新数据
  onPullDownRefresh() {
    this.init()
    setTimeout(() => {
      uni.stopPullDownRefresh()
    }, 1000)
  },
  onShow() {
    this.init()
    console.log('1122process.env', process.env)
  },
}
</script>

<style lang="scss" scoped>
.page-wrap {
  min-height: 100vh;
  background: #eef1f4;
}
.header-wrap {
  position: relative;
  .img {
    width: 100%;
  }
  .title {
    position: absolute;
    top: 100rpx;
    display: block;
    width: 100%;
    z-index: 1;
    text-align: center;
    font-family: 'Source Han Sans-Medium';
    font-weight: 500;
    font-size: 36rpx;
  }
  .nums-container {
    position: absolute;
    top: 200rpx;
    left: 30rpx;
    width: calc(100% - 60rpx);
    z-index: 1;
    border-radius: 20rpx;
    overflow: hidden;
    .label-item {
      padding: 15rpx 30rpx;
      height: 90rpx;
      background-size: cover;
      box-sizing: border-box;
      image {
        display: inline-block;
        width: 60rpx;
        height: 60rpx;
        vertical-align: middle;
      }
      text {
        margin-left: 30rpx;
        display: inline-block;
        vertical-align: middle;
        font-size: 36rpx;
        color: #191919;
        font-weight: 400;
        font-family: 'Source Han Sans-Regular';
      }
    }
    .nums-wrap {
      background: #ffffff;
    }
    .nums-item {
      display: flex;
      .item {
        padding: 60rpx 0;
        width: 50%;
        box-sizing: border-box;
        label {
          display: block;
          font-family: 'DINA-Regular';
          font-weight: 400;
          font-size: 60rpx;
          color: #191919;
          text-align: center;
        }
        text {
          display: block;
          font-family: 'Source Han Sans-Regular';
          font-weight: 400;
          font-size: 32rpx;
          color: #999999;
          text-align: center;
        }
      }
    }
    .divider {
      width: 100%;
      height: 1rpx;
      background-color: #e4e4e4;
    }
    .checkTotal {
      padding: 20rpx 0;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 28rpx;
      color: #191919;
      .icon {
        margin-left: 20rpx;
      }
    }
  }
}
.service-wrap {
  margin: 186rpx 30rpx 0 30rpx;
  .title {
    display: block;
    font-weight: 500;
    font-family: 'Source Han Sans-Medium';
    color: #191919;
    font-size: 36rpx;
    text-indent: 10rpx;
  }
  .options-item {
    margin: 30rpx 0;
    padding: 35rpx 40rpx;
    background: #ffffff;
    border-radius: 20rpx;
    box-sizing: border-box;
    .pro-ico {
      margin-right: 40rpx;
      display: inline-block;
      width: 70rpx;
      height: 70rpx;
      vertical-align: middle;
    }
    .item-name {
      font-size: 36rpx;
      color: #222222;
      font-family: 'Source Han Sans-Regular';
      font-weight: 400;
    }
    .next-ico {
      margin-top: 15rpx;
      float: right;
      display: block;
      width: 18rpx;
      height: 30rpx;
    }
  }
}
.exit-btn {
  position: fixed;
  right: 0rpx;
  bottom: 100rpx;
  // width: calc(100% - 60rpx);
  padding: 10rpx 30rpx 10rpx 50rpx;
  box-sizing: border-box;
  background: #ffffff;
  border-radius: 100rpx 0 0 100rpx;
  font-family: 'Source Han Sans-Regular';
  font-size: 30rpx;
  color: #191919;
}
.exit-btn::after {
  border: 0;
}
.flex-c {
  display: flex;
  align-items: center;
}
.flex-b {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
