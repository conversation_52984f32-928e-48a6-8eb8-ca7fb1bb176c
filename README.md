# 爱金华运营版移动端

## 项目用到的库
[uniapp](https://zh.uniapp.dcloud.io/): 多端跨平台框架
[uView](https://www.uviewui.com/components/intro.html): uni-app生态专用的UI框架

## 分包模块
├─community  分包-爱社区，社工端      
│  ├─errand  邻里互助模块 
│  └─social  社工端模块
├─enterprise  分包-企业数据宝，驻航招商办 (涉及到echarts图表相关的放置在此分包下)     
│  └─others  涉及到其他的零散的需要用到echarts的页面，放置在此目录下
├─finance  分包-爱金融
├─life   主包-爱生活主页
├─lifes  分包-爱生活
├─my  分包-我的
│  ├─auth  我的授权模块    
│  ├─community  我的社区模块   
│  ├─company  我的单位模块                     
│  ├─enter  社区点位录入模块
│  ├─feedback  意见反馈模块
│  ├─integral  我的积分模块        
│  ├─login  登录注册模块
│  ├─setting  设置模块
├─myIndex  主包-我的   
├─shouye  主包-首页    
└─union  分包-爱工会

## 项目注意事项
1. 为了同时兼容H5和微信小程序两端的图表，采用了lime-echart组件（后续有echart图表相关的都放到enterprise分包目录下）。
2. 由于微信小程序端分包不能大于2M，直接引入全部echarts.js会使得体积过大，我们采用了[echarts的按需定制的](https://echarts.apache.org/zh/builder.html)，目前只勾选了当前用到的图表（柱状图，折线图，饼图，散点图，雷达图，树图，地图，线图，关系图，仪表盘），下次有别的新图表要用可以去官网重新勾选进行定制化，生成新的echarts.min.js，然后替换enterprise/components/lime-echart/static目录下的echarts.min.js文件即可。
3. 该项目app端实际上时运行的H5端，对应的H5链接用iframe套在了[爱金华app的壳项目](http://192.168.110.130:8933/szjh/fe/ajh-operate-client)里面，如遇到了app相关的一些功能，需要和壳发送消息进行交互即可，本地app真机调试（打包发布）生成app需要用app的壳进行运行（发布）成安卓或者ios。
4. 列表下拉刷新，上拉加载更多，用z-paging组件，具体使用方法看[z-paging文档](https://www.uviewui.com/components/paging.html)。