import { http } from '@/utils/http'

// 统一市民用户登录
export const NormalLogin = (data) => {
  const params = {
    url: '/screen/ajhApp/login',
    method: 'POST',
    data,
  }
  return http(params)
}

// 统一市民用户登录
export const EnterpriseLogin = (data) => {
  const params = {
    url: '/screen/ajhApp/entLogin',
    method: 'POST',
    data,
  }
  return http(params)
}

// 市民注册
export const NormalRegister = (data) => {
  const params = {
    url: '/screen/ajhUser/register',
    method: 'POST',
    data,
  }
  return http(params)
}

// 市民账号忘记密码
export const ForgetPassword = (data) => {
  const params = {
    url: '/screen/ajhApp/forgotPassword',
    method: 'POST',
    data,
  }
  return http(params)
}

// 企业账号忘记密码
export const ForgetEntPassword = (data) => {
  const params = {
    url: '/screen/ajhApp/entForgotPassword',
    method: 'POST',
    data,
  }
  return http(params)
}

// 根据userId获取app用户信息详细信息
export const getUserInfoById = (userId) => {
  const params = {
    url: `/screen/ajhUser/${userId}`,
    method: 'GET',
  }
  return http(params)
}

// 修改app用户信息（通用）
export const editAjhuserInfo = (data) => {
  const params = {
    url: '/screen/ajhUser',
    method: 'PUT',
    data,
  }
  return http(params)
}

// 手势登录
export const GestureLogin = (data) => {
  const params = {
    url: '/screen/ajhApp/gestureLogin',
    method: 'POST',
    data,
  }
  return http(params)
}

// 企业账号注册
export const CompanyRegister = (data) => {
  const params = {
    url: '/screen/ajhEntUser/register',
    method: 'POST',
    data,
  }
  return http(params)
}

// 根据设备id查询最近登录用户
export const queryUserByDeviceId = (deviceId) => {
  const params = {
    url: `/screen/ajhApp/queryUserByDeviceId?deviceId=${deviceId}`,
    method: 'GET',
  }
  return http(params)
}

// 短信发送验证码
export const getMsgCodeByPhone = (phone) => {
  const params = {
    url: `/screen/unicom/send/${phone}`,
    method: 'GET',
  }
  return http(params)
}

// 短信验证码登录
export const msgCodeLogin = (data) => {
  const params = {
    url: '/screen/ajhApp/codeLogin',
    method: 'POST',
    data,
  }
  return http(params)
}

// 用户修改密码
export const editPassword = (data) => {
  const params = {
    url: '/screen/ajhUser/editPassword',
    method: 'POST',
    data,
  }
  return http(params)
}

// 人脸核身短信验证
export const faceSmsVerification = (data) => {
  const params = {
    url: '/screen/tecentFace/faceSmsVerification',
    method: 'POST',
    data,
  }
  return http(params)
}

// 照片人脸核身
export const faceRecognition = (data) => {
  const params = {
    url: '/screen/tecentFace/faceRecognition',
    method: 'POST',
    data,
  }
  return http(params)
}
// 绑定浙里办账号
export const bindZlb = (data) => {
  const params = {
    url: `/screen/ajhUser/bindZlb?ticket=${data}`,
    method: 'POST',
  }
  return http(params)
}

// irs身份信息认证
export const IrsverifyIdentity = (data) => {
  const params = {
    url: `/screen/tecentFace/IRSverifyIdentity`,
    method: 'POST',
    data,
  }
  return http(params)
}

// irs身份信息照片认证
export const IrsverifyFaceIdentity = (data) => {
  const params = {
    url: `/screen/tecentFace/IRSverifyFaceIdentity?idCard=${data.idCard}&imgUrl=${data.imgUrl}&name=${data.name}`,
    method: 'POST',
  }
  return http(params)
}

// 检验身份证是否已经绑定过
export const checkIfBind = (data) => {
  const params = {
    url: `/screen/tecentFace/ifBind?idCard=${data.idCard}`,
    method: 'POST',
  }
  return http(params)
}

// 验证码校验
export const verificationCode = (data) => {
  const params = {
    url: `/screen/ajhUser/verificationCode`,
    method: 'POST',
    data,
  }
  return http(params)
}

// 二要素认证
export const twoFactorCertification = (data) => {
  const params = {
    url: `/screen/system/htjc/twoFactorCertification`,
    method: 'POST',
    data,
  }
  return http(params)
}
