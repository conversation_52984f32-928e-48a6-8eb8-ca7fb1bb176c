<template>
  <view class="login-index">
    <view class="login-main">
      <view class="login-main-title">账号密码登录</view>
      <view class="login-main-form">
        <u--form
          ref="uForm"
          label-position="left"
          :model="form"
          :rules="rules"
          label-width="60"
        >
          <u-form-item label="账号：" prop="user" border-bottom>
            <u--input
              v-model="form.user"
              border="none"
              placeholder="请输入账号"
            ></u--input>
          </u-form-item>
          <u-form-item label="密码：" prop="password" border-bottom>
            <u--input
              v-model="form.password"
              type="password"
              placeholder="请输入密码"
              border="none"
            ></u--input>
          </u-form-item>
          <view class="submit-row">
            <u-button
              v-if="!subIng"
              type="primary"
              size="large"
              shape="circle"
              color="#FF3C00"
              @tap="submit"
            >
              登录
            </u-button>
            <u-button
              v-if="subIng"
              type="primary"
              loading
              shape="circle"
              size="large"
              color="#FF3C00"
              loading-text="登录中"
            ></u-button>
          </view>
        </u--form>
      </view>
      <view class="read-row">
        <checkbox-group @change="checkboxChange">
          <checkbox color="#fd0000" value="1" />
          <text>我已阅读并同意</text>
          <text class="link" @tap="toYsxy">《用户隐私协议》</text>
          和
          <text class="link" @tap="toYszc">《隐私政策条款》</text>
        </checkbox-group>
      </view>
    </view>
  </view>
</template>

<script>
import { mapMutations } from 'vuex'
import { queryUserByDeviceId } from '@/services/login'
import { yfkshLogin } from '@/services/yfk/shdl'
import base from '@/utils/base64.js'
export default {
  data() {
    return {
      form: {
        user: '',
        password: '',
      },
      rules: {
        user: {
          required: true,
          message: '请输入账号',
          trigger: ['blur', 'change'],
        },
        password: [
          {
            // 自定义验证函数，见上说明
            validator: (rule, value, callback) => {
              console.log('password-validator')
              console.log(rule)
              const check =
                /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\\W_!@#$%^&*`~()-+=.,。]+$)(?![a-z0-9]+$)(?![a-z\\W_!@#$%^&*`~()-+=.,。]+$)(?![0-9\\W_!@#$%^&*`~()-+=.,。]+$)[a-zA-Z0-9\\W_!@#$%^&*`~()-+=.,。]{8,30}$/
              // /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\\W_!@#$%^&*`~()-+=.,。]+$)(?![a-z0-9]+$)(?![a-z\\W_!@#$%^&*`~()-+=.,。]+$)(?![0-9\\W_!@#$%^&*`~()-+=.,。]+$)[a-zA-Z0-9\\W_!@#$%^&*`~()-+=.,。]{8,30}$/
              return check.test(value)
            },
            message:
              '密码长度不能少于8位，且应包含大写字母、小写字母、数字和特殊符号(@#$%^&*,.。)中至少三种',
            // 触发器可以同时用blur和change
            trigger: ['change', 'blur'],
          },
        ],
      },
      check: '',
      deviceId: '',
      subIng: false,
    }
  },
  methods: {
    ...mapMutations(['setUserInfo', 'login', 'setLastLoginUser']),
    checkboxChange(e) {
      this.check = e.detail.value.length ? '1' : ''
    },
    submit() {
      if (!this.check) {
        uni.$u.toast('请阅读并勾选用户隐私协议')
        return
      }
      console.log(this.form.password)
      this.$refs.uForm
        .validate()
        .then((res) => {
          console.log(res)
          // var base = new base()
          const params = {
            username: this.form.user,
            password: base.encode(this.form.password),
          }
          this.subIng = true
          yfkshLogin(params)
            .then((res) => {
              if (res.code === 200) {
                console.log(res)
                console.log(res.code === 200, '跳转商户首页')
                uni.reLaunch({
                  url: '/pages/boss/index',
                })
                const user = {
                  token: res.token,
                  userId: res.userId,
                }
                this.login(user)
              } else {
                uni.showToast({
                  icon: 'none',
                  title: res.msg || res.message,
                })
              }
              this.subIng = false
            })
            .catch((errors) => {
              console.log('11', errors)
              this.subIng = false
            })
        })
        .catch((errors) => {
          console.log(errors)
        })
    },
    toYsxy() {
      uni.navigateTo({
        url: '/pages/my/login/agreement',
      })
    },
    toYszc() {
      uni.navigateTo({
        url: '/pages/my/login/privacy',
      })
    },
    async fetchLastLogin() {
      if (!uni.getStorageSync('systemInfo')) {
        return
      }
      this.deviceId = uni.getStorageSync('systemInfo').deviceId
      const d = await queryUserByDeviceId(this.deviceId)
      console.log(d)
      if (d.code === 200 && d.result) {
        this.setLastLoginUser(d.result)
      }
    },
  },
  onLoad() {
    // 如果需要兼容微信小程序，并且校验规则中含有方法等，只能通过setRules方法设置规则。
    // #ifdef MP-WEIXIN
    this.$refs.uForm.setRules(this.rules)
    // #endif
  },
  onShow() {
    // #ifdef H5
    this.fetchLastLogin()
    // #endif
  },
}
</script>

<style lang="scss" scoped>
.login-index {
  .fr-entry {
    display: flex;
    justify-content: flex-end;
    padding: 30rpx 40rpx 20rpx;
    text-align: right;
    font-size: 32rpx;
  }

  .login-main {
    padding: 30rpx 64rpx;

    .login-main-title {
      font-weight: 500;
      font-size: 36rpx;
      color: #191919;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin-bottom: 34rpx;
    }

    ::v-deep .u-form-item__body {
      padding: 40rpx 0 32rpx 0;
      display: block;
    }

    ::v-deep .u-form-item__body__right {
      margin-top: 16rpx;
    }

    .login-main-form {
      .register-row {
        margin: 28rpx 0;
        color: #646464;
      }

      .submit-row {
        margin-top: 129rpx;

        .yzm-btn {
          color: #646464;
          text-align: center;
          height: 100rpx;
          line-height: 100rpx;
        }
      }
    }

    .login-entry {
      .login-entry-top {
        position: relative;
        text-align: center;
        font-size: 28rpx;
        color: #999;
      }

      .login-entry-top::before {
        position: absolute;
        z-index: 20;
        left: 52rpx;
        content: '';
        top: 16rpx;
        width: 200rpx;
        height: 1px;
        background: #e8e8e8;
      }

      .login-entry-top::after {
        position: absolute;
        z-index: 20;
        right: 52rpx;
        content: '';
        top: 16rpx;
        width: 200rpx;
        height: 1px;
        background: #e8e8e8;
      }

      .entry-navs {
        display: flex;
        justify-content: center;
        margin-top: 20rpx;

        .entry-nav-item {
          margin: 0 50rpx;
          font-size: 26rpx;

          .icon {
            width: 100rpx;
            margin-bottom: 12rpx;
          }
        }
      }
    }

    .read-row {
      margin-top: 80rpx;
      padding: 0 40rpx;
      color: #646466;
      font-size: 28rpx;
      line-height: 1.6;
      ::v-deep uni-checkbox:not([disabled]) .uni-checkbox-input:hover {
        border-color: #ffa200;
      }
      .link {
        color: #428ffc;
      }
    }
  }

  .zlb-loading {
    .u-loading-icon {
      margin-top: 44vh;
    }
  }
}
</style>
