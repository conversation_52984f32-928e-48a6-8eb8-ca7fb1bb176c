export default {
  data() {
    return {
    }
  },
  onShareAppMessage(e) {
    return {
      title: '爱金华微信小程序',
      // imageUrl: this.$getStaticImg('img/my/login/wxlogin-applogo.png'), // 自定义图片路径，支持PNG及JPG，不传入 imageUrl 则使用默认截图。显示图片长宽比是 5:4
      success(d) {
        // 转发成功之后的回调
        console.log(d, '分享成功')
      },
      fail(res) {
        console.log(res, '分享失败')
        // 转发失败之后的回调
        if (res.errMsg == 'shareAppMessage:fail cancel') {
          // 用户取消转发
        } else if (res.errMsg == 'shareAppMessage:fail') {
          // 转发失败，其中 detail message 为详细失败信息
        }
      },
      complete(e) {
        // 转发结束之后的回调（转发成不成功都会执行）
        console.log(e)
      },
    }
  },
  onShareTimeline: function () {
    return {
      title: '爱金华微信小程序',
      // query: 'from=pyq',
      // imageUrl: this.$getStaticImg('img/setting/icon.png'),
    }
  },
}
