<template>
  <view>
    <view class="header">
      <image
        :src="$getStaticImg('img/yfk/login/bg-fk.png')"
        mode=""
        class="header-img"
      />
    </view>
    <view class="logo-container">
      <image
        :src="$getStaticImg('img/yfk/login/userLogin-fk.png')"
        mode=""
        class="logo-img"
      />
      <text class="logo-text">爱生活·商户端</text>
    </view>
    <view class="btns-container">
      <button @tap="toLoginPwd">账号密码登录</button>
    </view>
    <!-- <view class="footer">
      <text class="topath" @tap="toBossLogin">用户端登录 ></text>
    </view> -->
  </view>
</template>
<script>
export default {
  data() {
    return {
      check: '',
    }
  },
  methods: {
    // 跳转至用户端登录页
    toBossLogin() {
      // #ifdef H5
      uni.navigateTo({
        url: '/pages/yfk_login/index',
      })
      // #endif
      // #ifdef MP-WEIXIN
      uni.navigateTo({
        url: '/pages/login/wxLogin',
      })
      // #endif
    },
    // 跳转至账号密码登录页
    toLoginPwd() {
      uni.navigateTo({
        url: '/pages/yfk_login/pwd',
      })
    },
  },
}
</script>
<style lang="scss" scoped>
.header-img {
  display: block;
  width: 100%;
}
.logo-container {
  position: relative;
  top: -100rpx;
  height: 300rpx;
  .logo-img {
    margin: 0 auto 60rpx;
    display: block;
    width: 180rpx;
    height: 180rpx;
  }
  .logo-text {
    display: block;
    font-family: 'Alimama FangYuanTi VF-SemiBold-Square';
    font-weight: 600;
    font-size: 48rpx;
    color: #3d3d3d;
    text-align: center;
  }
}
.btns-container {
  padding: 64rpx;
  font-family: 'Source Han Sans-Medium';
  button {
    margin-bottom: 40rpx;
    line-height: 96rpx;
    background: #FF3C00;
    border-radius: 100rpx;
    font-weight: 500;
    color: #ffffff;
    font-size: 32rpx;
  }
}
.footer {
  position: absolute;
  bottom: 120rpx;
  width: 100%;
  text-align: center;
  color: #646466;
  font-size: 28rpx;
  line-height: 1.6;
  ::v-deep uni-checkbox:not([disabled]) .uni-checkbox-input:hover {
    border-color: #fa0000;
  }
  .topath {
    margin-bottom: 40rpx;
    display: block;
    font-family: 'Source Han Sans-Regular';
    font-size: 28rpx;
    color: #191919;
    text-align: center;
    font-weight: 400;
  }
  .link {
    color: #428ffc;
  }
}
</style>
