<template>
  <view>
    <u-upload
      accept="file"
      :file-list="fileList"
      :max-count="maxCount"
      :preview-full-image="false"
      :preview-image="false"
      :max-size="maxSize * 1024 * 1024"
      :disabled="disabled"
      @afterRead="afterRead"
      @delete="deletePic"
      @oversize="oversize"
    >
      <slot>
        <u-button type="primary">上传文件</u-button>
      </slot>
    </u-upload>
    <view class="file-list">
      <view v-for="item of fileList" :key="item.url" class="file-item">
        <view class="progress">
          <u-line-progress :percentage="100"></u-line-progress>
        </view>
        <view class="flex-between-c">
          <view class="file-name">{{ item.name }}</view>
          <view class="del-file-btn" @tap="deletePic(item)">
            <u-icon name="close-circle-fill" size="24" color="#f56c6c"></u-icon>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { uploadHttp } from '@/utils/http'
export default {
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    maxCount: {
      type: Number,
      default: 3,
    },
    maxSize: {
      type: Number,
      default: 10,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      fileList: [],
    }
  },
  watch: {
    fileList: {
      handler(newName, oldName) {
        console.log(newName)
        this.$emit('input', this.fileList)
      },
      // immediate: true,
      deep: true,
    },
    value: {
      handler(newVal, oldVal) {
        this.fileList = newVal
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    afterRead(e) {
      console.log(e, 'afterRead')
      uploadHttp(e.file.url).then((res) => {
        console.log(res, 'uploadHttp')
        if (res.code === 200) {
          const d = res.result
          this.fileList.push({
            ...e.file,
            url: d.proxyPath + d.relativePath,
            result: d,
            status: 'success',
            message: '',
          })
          console.log(this.fileList)
        }
      })
    },
    deletePic(e) {
      console.log(e, 'deletePic')
      const index = this.fileList.findIndex((f) => f.result.id === e.result.id)
      this.fileList.splice(index, 1)
    },
    oversize(file) {
      console.log(file, 'oversize')
      uni.showToast({
        icon: 'none',
        title: `图片最大不能超过${this.maxSize}MB`,
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.file-list {
  margin: 24rpx 0;
  .file-item {
    position: relative;
    margin: 10rpx 0;
    .progress {
      margin-bottom: 8rpx;
    }
    .file-name {
      margin-top: 10rpx;
      color: #3c9cff;
      font-size: 28rpx;
    }
    .del-file-btn {
      position: absolute;
      z-index: 20;
      right: 30rpx;
      bottom: 0rpx;
    }
  }
}
</style>
