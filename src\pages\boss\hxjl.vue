<template>
  <view>
    <view class="search-box">
      <view class="search-area">
        <view class="input-area">
          <u-input
            placeholder="请输入订单号"
            border="none"
            placeholder-style="color: #a5a5a5;"
            clearable
            v-model="searchYfk"
          ></u-input>
        </view>
        <view class="search-btn" @tap="search">搜索</view>
      </view>
    </view>
    <u-tabs
      :list="tabs"
      @click="tabclick"
      lineWidth="70rpx"
      lineColor="#FFA200"
      itemStyle="height: 96rpx;margin:0;padding:0;width:33%;background: #EEF1F4;"
      inactiveStyle="color: #999999;font-size: 32rpx;"
      activeStyle="color: #191919;font-size: 32rpx;"
    ></u-tabs>
    <view>
      <view class="container-hxjl">
        <view class="filter_box">
          <view class="container-time-hxjl" @click="show = true">
            <text v-if="!valueyfk">
              {{ valueyfk0 }}
            </text>
            <text v-else>{{ valueyfk }}</text>
            <image src="@/static/img/yfk/btn.png" class="header_pic"></image>
          </view>
        </view>

        <u-datetime-picker
          ref="datetimePicker"
          :show="show"
          mode="year-month"
          :formatter="formatter"
          @cancel="show = false"
          @confirm="confirm"
          v-model="valueyfk0"
        ></u-datetime-picker>

        <view class="content-product">
          <view class="container-box-hxjl" v-if="listdata.length">
            <view
              v-for="(item, index) in listdata"
              :key="index"
              class="hxjl_item"
            >
              <view class="hxjl_item_top">
                <view class="hxjl_num" v-if="tabs_active == 1">
                  <image
                    :src="$getStaticImg('img/yfk/login/hxjl_jek.png')"
                    mode="aspectFill"
                    class="hxjl_icon"
                  ></image>
                  <text class="hxjl_num_text">扫码核销</text>
                </view>
                <view class="hxjl_num" v-if="tabs_active == 2">
                  <image
                    :src="$getStaticImg('img/yfk/login/hxjl_yhq.png')"
                    mode="aspectFill"
                    class="hxjl_icon"
                  ></image>
                  <text class="hxjl_num_text">优惠券核销</text>
                </view>
                <view class="hxjl_num" v-if="tabs_active == 3">
                  <text class="hxjl_num_text">商品核销</text>
                </view>
                <view
                  class="hxjl_num"
                  v-if="tabs_active == 2 || tabs_active == 3"
                ></view>
                <view v-if="tabs_active == 1">
                  <view class="hxjl_status" v-if="item.mxType == '已核销'">
                    {{ item.mxType }}
                  </view>
                  <view class="hxjl_status gray" v-if="item.mxType == '已退款'">
                    {{ item.mxType }}
                  </view>
                  <view
                    class="hxjl_status gray"
                    v-if="item.mxType == '支付失败'"
                  >
                    {{ item.mxType }}
                  </view>
                </view>
              </view>
              <view class="hxjl_item_center" v-if="tabs_active == 1">
                <!-- <view class="hxjl_item_form">
                  <view class="name">卡名：</view>
                  <view class="value">{{ item.spmc }}</view>
                </view> -->
                <view class="hxjl_item_form">
                  <view class="name">订单号：</view>
                  <view class="value">{{ item.orderId }}</view>
                </view>
                <view class="hxjl_item_form">
                  <view class="name">手机号码：</view>
                  <view class="value">{{ item.phone }}</view>
                </view>
                <view class="hxjl_item_form">
                  <view class="name">核销金额：</view>
                  <view class="num">{{ item.amount }}</view>
                </view>
                <view class="hxjl_item_form" v-if="item.yhje">
                  <view class="name">优惠金额：</view>
                  <view class="num">{{ item.yhje }}</view>
                </view>
                <view class="hxjl_item_form" v-if="item.tkStatus == 1">
                  <view class="name">退款金额：</view>
                  <view class="num">{{ item.amount }}</view>
                </view>
              </view>
              <view class="hxjl_item_center" v-if="tabs_active == 2">
                <view class="hxjl_item_form">
                  <view class="name">优惠券编号：</view>
                  <view class="value">{{ item.yhqId }}</view>
                </view>
                <view class="hxjl_item_form">
                  <view class="name">核销方式：</view>
                  <view class="value">线下核销</view>
                </view>
                <view class="hxjl_item_form">
                  <view class="name">核销时间：</view>
                  <view class="value">{{ item.hxTime }}</view>
                </view>
              </view>
              <view class="hxjl_item_center" v-if="tabs_active == 3">
                <view class="hxjl_item_form">
                  <view class="name">订单编号：</view>
                  <view class="value">{{ item.orderId }}</view>
                </view>
                <view class="hxjl_item_form">
                  <view class="name">商品名称：</view>
                  <view class="value">{{ item.spmc }}</view>
                </view>
                <view class="hxjl_item_form">
                  <view class="name">手机号：</view>
                  <view class="value">{{ item.phone }}</view>
                </view>
                <view class="hxjl_item_form">
                  <view class="name">核销时间：</view>
                  <view class="value">{{ item.hxTime }}</view>
                </view>
              </view>
              <view class="hxjl_item_bottom" v-if="tabs_active == 1">
                <view class="hxjl_item_date">{{ item.hxTime }}</view>
                <view
                  class="hxjl_item_yhx"
                  v-if="item.tkStatus !== '1'"
                  @tap="refund(item)"
                >
                  申请退款
                </view>
              </view>
            </view>
            <u-divider
              v-if="listdata.length > 0"
              :text="moreFlag ? '下拉加载更多' : '没有更多了'"
            ></u-divider>
          </view>
          <view class="content-product-zwjl" v-else>
            <image
              :src="$getStaticImg('img/fyk/login/nodata.png')"
              mode=""
              class="logo-img"
            />
            <text>暂无数据</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { hxjlrefund, shopHxjlAbdYhqjl } from '@/services/yfk/boss/hxjl.js'
export default {
  data() {
    return {
      tabs_active: 1,
      tabs: [{ name: '余额核销' }, { name: '券核销' }, { name: '商品核销' }],
      searchYfk: '',
      //
      show: false,
      cardId: '',
      valueyfk0: '',
      valueyfk: '',
      moreFlag: true,
      listdata: [],
      params: {
        pageSize: 10,
        pageNum: 1,
        total: 10,
      },
      showFlag: false,
      //优惠卷核销

      valueyhj0: '',
      valueyhj: '',
      yhjListdata: [],
      yhjmoreFlag: true,
      yhjparams: {
        pageSize: 10,
        pageNum: 1,
        total: 10,
      },
    }
  },
  onLoad() {
    const currentDate = new Date()
    const year = currentDate.getFullYear() // 获取年份，例如：2022
    let month = currentDate.getMonth() + 1
    month = month < 10 ? '0' + month : month
    this.valueyfk0 = year + '-' + month
    this.valueyfk = this.valueyfk0
    // this.valueyhj0 = year + '-' + month
    // this.valueyhj = this.valueyhj0
    this.getData()
  },
  onReady() {
    this.$refs.datetimePicker.setFormatter(this.formatter)
    // this.$refs.datetimePicker1.setFormatter(this.formatter)
  },
  methods: {
    // tab切换事件
    tabclick(item) {
      // if (item.index == 0) {
      //   this.params.pageNum = 1
      //   this.listdata = []
      // } else {
      //   this.yhjparams.pageNum = 1
      //   this.yhjListdata = []
      // }
      this.listdata = []
      this.tabs_active = item.index + 1
      this.getData()
    },
    //查询店铺优惠券
    getData() {
      let params = {
        type: this.tabs_active, //1.余额核销，2优惠券核销，3商品核销
        phone: this.searchYfk,
        pageNum: this.params.pageNum,
        pageSize: this.params.pageSize,
        cxsj: this.valueyfk,
      }
      shopHxjlAbdYhqjl(params).then((res) => {
        console.log(res)
        if (this.tabs_active == 1) {
          this.listdata = this.listdata.concat(res.data.list)
          this.listdata.map((res) => {
            if (res.mxType == 2) res.mxType = '支付失败'
            if (res.mxType == 3) res.mxType = '已核销'
            if (res.mxType == 4) res.mxType = '已退款'
          })
          this.params.total = res.data.total
          this.moreFlag = this.listdata.length == res.data.total ? false : true
        } else {
          this.listdata = this.listdata.concat(res.data.list)
          this.params.total = res.data.total
          this.moreFlag = this.listdata.length == res.data.total ? false : true
        }
      })
    },
    search() {
      this.listdata = []
      this.getData()
    },
    formatter(type, value) {
      if (type === 'year') {
        return `${value}年`
      }
      if (type === 'month') {
        return `${value}月`
      }
      return value
    },
    // 选择时间
    confirm(val) {
      const date = new Date(val.value)
      const year = date.getFullYear()
      let month = date.getMonth() + 1
      month = month < 10 ? '0' + month : month
      this.valueyfk0 = year + '-' + month
      this.valueyfk = this.valueyfk0
      this.show = false
      this.params.pageNum = 1
      this.listdata = []
      this.getData()
    },
    async refund(item) {
      uni.showModal({
        title: '提示',
        content: '是否确认申请退款？',
        cancelText: '取消',
        confirmText: '确定',
        success: async (s) => {
          if (s.confirm) {
            const a = await hxjlrefund({
              orderId: item.orderId,
            })
            if (a.code == 200) {
              uni.showToast({
                title: '退款成功~',
              })
              this.params.pageNum = 1
              this.listdata = []
              this.getData()
            } else {
              uni.showToast({
                title: a.msg,
                icon: 'error',
              })
            }
          } else {
            uni.showToast({
              title: '取消申请',
              icon: 'error',
            })
          }
        },
      })
    },
  },
  // 下拉刷新订单列表
  onPullDownRefresh() {
    this.listdata = []
    this.getData()
    setTimeout(() => {
      uni.stopPullDownRefresh()
    }, 1000)
  },
  // 上拉加载订单列表
  async onReachBottom() {
    if (this.tabs_active == 1) {
      if (this.moreFlag && this.listdata.length > 0) {
        this.params.pageNum++
        this.getData()
      }
    } else {
      if (this.yhjmoreFlag && this.yhjListdata.length > 0) {
        this.yhjparams.pageNum++
        this.getData()
      }
    }
  },
}
</script>
<style lang="scss" scoped>
.search-box {
  padding: 16rpx 30rpx;
  background: #fff;

  .search-area {
    display: flex;
    align-items: center;
    height: 62rpx;
    background: #f5f5f7;
    border-radius: 18rpx;

    .scan-area {
      padding: 0 26rpx;
      border-right: 1px solid #959da5;
    }
  }

  .input-area {
    padding: 0 24rpx;
    flex: 1;
  }

  .search-btn {
    width: 114rpx;
    height: 56rpx;
    margin-right: 6rpx;
    line-height: 56rpx;
    text-align: center;
    border-radius: 20rpx;
    font-size: 28rpx;
    color: #fff;
    background: #FF3C00;
  }
}

.container-hxjl {
  background: #eef1f4;
  padding: 30rpx 0;
  min-height: calc(100vh - 290rpx);

  .filter_box {
    display: flex;
    align-content: center;
    align-items: center;
    .container-time-hxjl {
      width: 166rpx;
      height: 60rpx;
      line-height: 60rpx;
      padding-left: 24rpx;
      background: #ffffff;
      margin-left: 30rpx;
      border-radius: 20rpx;
      font-weight: 400;
      font-size: 26rpx;
      font-style: normal;
      text-transform: none;

      text {
        float: left;
      }

      image {
        float: right;
        width: 22rpx;
        height: 13rpx;
        text-align: right;
        margin-right: 16rpx;
        margin-top: 24rpx;
      }
    }
  }

  .content-product-zwjl {
    .logo-img {
      width: 350rpx;
      height: 350rpx;
      display: block;
      margin: 113rpx auto;
      margin-bottom: 0;
    }

    text {
      margin-top: 20rpx;
      display: block;
      text-align: center;
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 400;
      font-size: 36rpx;
      color: #191919;
      line-height: 36rpx;
      text-align: center;
      font-style: normal;
      text-transform: none;
    }
  }
}

.container-box-hxjl {
  margin: 30rpx 26rpx 0 26rpx;

  .hxjl_item {
    background: #ffffff;
    border-radius: 20rpx 20rpx 20rpx 20rpx;
    padding: 30rpx 20rpx;
    margin-bottom: 30rpx;
    .hxjl_item_top {
      display: flex;
      justify-content: space-between;
      align-content: flex-start;
      align-items: flex-start;
      .hxjl_num {
        margin-right: 12rpx;
        display: flex;
        align-content: center;
        align-items: center;
        flex: 1;
        .hxjl_icon {
          width: 60rpx;
          height: 60rpx;
        }
        .hxjl_num_text {
          font-family: Source Han Sans, Source Han Sans;
          font-weight: 400;
          font-size: 36rpx;
          color: #191919;
          line-height: 36rpx;
          margin-left: 20rpx;
        }
        .hxjl_num_text1 {
          word-break: break-all;
          font-family: Source Han Sans, Source Han Sans;
          font-weight: 400;
          font-size: 28rpx;
          color: #191919;
          line-height: 36rpx;
          margin-left: 20rpx;
        }
      }
      .hxjl_status {
        // width: 123rpx;
        padding: 0 15rpx;
        height: 50rpx;
        background: rgba(255, 60, 0, 0.1);
        border-radius: 10rpx 10rpx 10rpx 10rpx;
        font-family: Source Han Sans, Source Han Sans;
        font-weight: 400;
        font-size: 26rpx;
        color: #FF3C00;
        line-height: 50rpx;
        text-align: center;
        &.gray {
          background: rgba(25, 25, 25, 0.05);
          color: #191919;
        }
      }
    }
    .hxjl_item_center {
      background: rgba(245, 246, 248, 0.5);
      border-radius: 20rpx 20rpx 20rpx 20rpx;
      padding: 15rpx 30rpx;
      margin-top: 22rpx;
      .hxjl_item_form {
        margin: 15rpx 0;
        display: flex;
        .name {
          font-family: Source Han Sans, Source Han Sans;
          font-weight: 400;
          font-size: 32rpx;
          color: #191919;
          line-height: 36rpx;
        }
        .value {
          font-family: Source Han Sans, Source Han Sans;
          font-weight: 400;
          font-size: 32rpx;
          color: #191919;
          line-height: 36rpx;
          word-break: break-all;
          flex: 1;
        }
        .num {
          font-weight: 700;
          font-size: 36rpx;
          line-height: 36rpx;
          color: #fd0000;
        }
      }
    }
    .hxjl_item_bottom {
      display: flex;
      justify-content: space-between;
      align-content: center;
      align-items: center;
      margin-top: 20rpx;
      .hxjl_item_date {
        font-family: Source Han Sans, Source Han Sans;
        font-weight: 400;
        font-size: 28rpx;
        color: #999999;
        line-height: 36rpx;
      }
      .hxjl_item_yhx {
        width: 150rpx;
        height: 56rpx;
        border-radius: 10rpx 10rpx 10rpx 10rpx;
        border: 1rpx solid #cccccc;
        font-family: Source Han Sans, Source Han Sans;
        font-weight: 400;
        font-size: 28rpx;
        color: #555555;
        line-height: 56rpx;
        text-align: center;
      }
    }
  }
}
</style>
