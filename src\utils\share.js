import { getWxJSSign } from '@/services/activity/index.js'
import { getStaticImg } from '@/utils/index.js'
import store from '@/store/index.js'
// 发起获取signature请求
function getSignature(actId) {
  console.log(window.location.href)
  getWxJSSign(window.location.href).then((res) => {
    if (res.code === 200) {
      setShare(res.data, actId)
    }
  })
}
// 设置分享数据
function setShare(signature, actId) {
  uni.setStorageSync('signature', JSON.stringify(signature))
  console.log(signature, 'signature-setShare')
  // var url = `${window.location.origin}/ajhhd/pages/collectedWorks/help?pullUserId=${store.state.userInfo.userId}&actId=${actId}`
  // console.log(actId, url)
  jWeixin.config({
    debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
    // 配置微信 JSSDK
    appId: signature.appId, // 必填，公众号的唯一标识
    timestamp: signature.timestamp, // 必填，生成签名的时间戳
    nonceStr: signature.nonceStr, // 必填，生成签名的随机串
    signature: signature.signature, // 必填，签名
    jsApiList: ['updateAppMessageShareData', 'updateTimelineShareData'],
  })
  jWeixin.error((res) => {
    console.log(res, 'jWeixin.error')
    // config信息验证失败会执行error函数，如签名过期导致验证失败，具体错误信息可以打开config的debug模式查看，也可以在返回的res参数中查看，对于SPA可以在这里更新签名。
    // getSignature()
  })
  console.log('doH5Share')
  console.log(window.location.origin)
  var url = `${window.location.origin}/ajhhd/#/pages/collectedWorks/help?pullUserId=${store.state.userInfo.userId}&actId=${actId}`
  jWeixin.ready(function () {
    console.log('这是分享功能')
    jWeixin.updateAppMessageShareData({
      imgUrl: getStaticImg('img/activity/honorOfKings/share-hd-img.png'), // 分享图标
      title: '求助力，我要吃金字火腿冰淇淋', // 分享标题
      link: url, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
      desc: '下一轮抢128元现金红包，别说我没告诉你', // 分享描述
      success: function () {
        console.log('我来分享了')
      },
      fail: (err) => {
        console.log(err, 'jWeixin.updateAppMessageShareData-err')
      },
    })
    jWeixin.updateTimelineShareData({
      desc: '下一轮抢128元现金红包，别说我没告诉你', // 分享描述
      title: '求助力，我要吃金字火腿冰淇淋', // 分享标题
      link: url, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
      imgUrl: getStaticImg('img/activity/honorOfKings/share-hd-img.png'), // 分享图标
      success: function () {
        console.log('我来分享了')
      },
      fail: (err) => {
        console.log(err, 'jWeixin.updateAppMessageShareData-err')
      },
    })
  })
}

// export const doH5Share = () => {
//   console.log('doH5Share')
//   var url = `${window.location.origin}/ajhhd/pages/collectedWorks/help?pullUserId=${store.state.userInfo.userId}&actId=10001`
//   jWeixin.ready(function () {
//     console.log('这是分享功能')
//     jWeixin.updateAppMessageShareData({
//       // imgUrl: 'https://xxx.com/static/ai.png', // 分享图标
//       title: '爱金华', // 分享标题
//       link: url, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
//       desc: '金华王者荣耀赛，免费集字赢大奖', // 分享描述
//       success: function () {
//         console.log('我来分享了')
//       },
//       fail: (err) => {
//         console.log(err, 'jWeixin.updateAppMessageShareData-err')
//       },
//     })
//     jWeixin.updateTimelineShareData({
//       desc: '金华王者荣耀赛，免费集字赢大奖', // 分享描述
//       title: '爱金华', // 分享标题
//       link: url, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
//       // imgUrl: 'https://xxx/static/ai.png', // 分享图标
//       success: function () {
//         console.log('我来分享了')
//       },
//       fail: (err) => {
//         console.log(err, 'jWeixin.updateAppMessageShareData-err')
//       },
//     })
//   })
// }

//分享
export const initShare = (actId) => {
  console.log('initShare')
  getSignature(actId)
  // const _signature = uni.getStorageSync('signature')
  // // 先看看缓存里有没有
  // if (_signature) {
  //   let signature = JSON.parse(_signature)
  //   // 获取当前时间戳
  //   var now = parseInt(new Date().getTime() / 1000) + ''
  //   // 计算两个时间戳之间的差值，单位为秒
  //   var diff = now - signature.expire_time

  //   // 判断差值是否大于2小时的秒数，即7200秒
  //   if (diff > 7200) {
  //     console.log('时间戳已经超过两小时。')
  //     getSignature()
  //   } else {
  //     console.log('时间戳未超过两小时。')
  //     setShare(signature)
  //   }
  // } else {
  //   getSignature()
  // }
}
