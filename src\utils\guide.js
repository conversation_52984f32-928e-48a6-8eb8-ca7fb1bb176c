export default function routeWatch() {
  const list = ['navigateTo', 'redirectTo', 'reLaunch', 'switchTab']
  const noLoginRoutes = [
    '/',
    '/pages/index/index',
    '/pages/wxlogin/index',
    '/pages/mine/index',
    '/pages/lifeCircle/index',
    '/pages/map/index',
  ]
  list.forEach((item) => {
    uni.addInterceptor(item, {
      invoke(args) {
        console.log(args, 'invoke')
        const url = args.url.split('?')[0]
        let pass
        if (noLoginRoutes) {
          pass = noLoginRoutes.some((item) => {
            if (typeof item === 'object' && item.pattern) {
              return item.pattern.test(url)
            }
            return url === item
          })
        }
        console.log(pass)
        // 不是白名单并且没有token
        if (!pass && !uni.getStorageSync('token')) {
          console.log('进来了')
          uni.navigateTo({
            url: '/pages/my/login/index',
          })
          return false
        }
        return args
      },
    })
  })
}
