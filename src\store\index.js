import Vue from 'vue'
import Vuex from 'vuex'
Vue.use(Vuex)

const handleStore = (store) => {
  if (uni.getStorageSync('store'))
    store.replaceState(JSON.parse(uni.getStorageSync('store'))) // 初始化store
  store.subscribe((mutation, state) => {
    uni.setStorageSync('store', JSON.stringify(state))
    // #ifdef H5
    window.parent.postMessage(
      {
        cmd: 'setStore',
        value: state,
      },
      '*'
    )
    // #endif
  })
}
const store_str = {
  user_name: '用户名',
  user_zw: '职务',
  user_dept: '单位',
  user_phone: '电话',
}
const store = new Vuex.Store({
  namespaced: true,
  state: {
    /**
     * 是否需要强制登录
     */
    forcedLogin: false,
    hasLogin: false,
    token: '',
    userId: null,
    hasPwd: false,
    openid: '',
    userInfo: {},
    collectId: [],
    lastLoginUser: {}, // 对应设备的上次登录用户信息
    baseInfo: {},
    ajhEntUser: {},
    currentTemplateCommunity: {},
    isFingerCheck: false, // 是否进行了指纹验证
    position: {
      // 系统默认位置信息
      // longitude: 119.5715,
      // 		latitude: 29.08624,
      // 		isDefault: 1, // 是否是用户拒绝定位后给的一个默认位置信息
      // 		address: {
      // 			poiName: '金华市婺城区人民政府(宾虹西路北)'
      // 		}
      longitude: null,
      latitude: null,
      address: null,
      // isDefault: 1, // 是否是用户拒绝定位后给的一个默认位置信息
      // address: {
      // 	poiName: '金华市婺城区人民政府(宾虹西路北)'
      // }
    }, // 用户的定位信息
  },
  getters: {
    getStore(state) {
      return state
    },
    getStoreStr() {
      return store_str
    },
  },
  mutations: {
    setCurrentTemplateCommunity(state, community) {
      state.currentTemplateCommunity = community
    },
    setAjhEntUser(state, ajhEntUser) {
      state.ajhEntUser = ajhEntUser
    },
    getCollectId(state, id) {
      state.collectId = id
    },
    login(state, user) {
      state.hasLogin = true
      Object.keys(user).forEach((key) => {
        state[key] = user[key]
      })
    },
    setUserInfo(state, userInfo) {
      state.userInfo = userInfo
    },
    setLastLoginUser(state, info) {
      state.lastLoginUser = info
    },
    setBaseInfo(state, baseInfo) {
      state.baseInfo = baseInfo
    },
    logout(state) {
      state.hasLogin = false
      state.token = ''
      state.userInfo = {}
      state.userId = null
      // Object.keys(state).forEach((key) => {
      //   if (~['hasLogin', 'forcedLogin', 'hasPwd'].indexOf(key))
      //     state[key] = false
      //   else if (key == 'cid') {
      //   } else state[key] = ''
      // })
    },
    setState(state, key, value) {
      state[key] = value
    },
    set_props(state, props) {
      Object.keys(props).forEach((key) => {
        state[key] = props[key]
      })
    },
    setHasPwd(state, bool) {
      state.hasPwd = bool
    },
    setopenId(state, bool) {
      state.openid = bool
    },
    setIsFingerCheck(state, bool) {
      state.isFingerCheck = bool
    },
    setPosition(state, position) {
      state.position = position
    },
  },
  actions: {},
  plugins: [handleStore],
})

// #ifdef H5

window.addEventListener('message', (event) => {
  if (event.data.cmd == 'initH5Store') {
    if (event.data.value) {
      store.replaceState(JSON.parse(event.data.value)) // 初始化store
      uni.setStorageSync('store', event.data.value)
    }
  }
})
// #endif

export default store
