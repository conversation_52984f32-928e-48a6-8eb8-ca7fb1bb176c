<template>
  <view>
    <view class="message-container">
      <image
        :src="$getStaticImg('img/yfk/login/hxsuccess.png')"
        class="message-img"
      />
      <text class="message-text">核销成功</text>
    </view>
    <view class="item-container">
      <view class="item" v-if="order.amount">
        <view class="item_left">核销金额</view>
        <view class="item_right" style="font-weight: 700">
          {{ order.amount }}元
        </view>
      </view>
      <view class="item">
        <view class="item_left">订单编号</view>
        <view class="item_right">{{ order.orderId }}</view>
      </view>
      <view class="item">
        <view class="item_left">核销时间</view>
        <view class="item_right">{{ order.payTime }}</view>
      </view>
    </view>
    <button class="back-btn" @tap="back">返回</button>
  </view>
</template>

<script>
import { orderDetails } from '@/services/yfk/myOrder'
export default {
  data() {
    return {
      order: {},
    }
  },
  methods: {
    // 返回
    back() {
      uni.reLaunch({
        url: '/pages/boss/index',
      })
    },
    // 获取订单详情
    orderDetails(orderId) {
      orderDetails(orderId).then((res) => {
        if (res.code == 200) {
          this.order = res.data
        }
      })
    },
  },
  onLoad(data) {
    this.orderDetails(JSON.parse(decodeURIComponent(data.orderId)))
  },
}
</script>

<style lang="scss" scoped>
page {
  background: #eef1f4;
}
.message-container {
  padding: 100rpx 0 120rpx;
  .message-img {
    margin: 0 auto 40rpx;
    display: block;
    width: 120rpx;
    height: 120rpx;
  }
  .message-text {
    display: block;
    text-align: center;
    font-size: 48rpx;
    color: #191919;
    font-family: 'Source Han Sans-Regular';
    font-weight: 400;
  }
}
.item-container {
  margin: 0 20rpx;
  background: #ffffff;
  border-radius: 20rpx;
  padding: 20rpx 30rpx;
  .item {
    display: flex;
    justify-content: space-between;
    align-content: flex-start;
    margin: 26rpx 0;
    .item_left {
      color: #999999;
      font-size: 30rpx;
      font-family: 'Source Han Sans-Regular';
      font-weight: 400;
    }
    .item_right {
      flex: 1;
      text-align: right;
      color: #191919;
      font-size: 30rpx;
      font-family: 'Source Han Sans-Regular';
      font-weight: 400;
      word-break: break-all;
    }
  }
}
.back-btn {
  position: fixed;
  bottom: 120rpx;
  left: 50%;
  transform: translate(-50%);
  width: 300rpx;
  line-height: 88rpx;
  background: #FF3C00;
  border-radius: 100rpx;
  color: #ffffff;
}
.back-btn::after {
  border: 0;
}
</style>
