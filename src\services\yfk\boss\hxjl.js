import { http } from '@/utils/http'

// 订单列表
export const hxjlrefund = (data) => {
    const params = {
     url: '/screen/fkpt/tAjhFkptOrder/refund',
     method: 'POST',
     data,
    }
    return http(params)
}

// 核销时商户根据用户id获取卡信息
export const getCardInfo = (data) => {
  const params = {
    url: '/screen/yfk/ajhYfkCard/getCardInfo',
    method: 'GET',
    data,
  }
  return http(params)
}

// 核销时商户判断用户有没有金额卡
export const getJeCard = (data) => {
  const params = {
    url: '/screen/yfk/ajhYfkCard/getJeCard',
    method: 'GET',
    data,
  }
  return http(params)
}

// 商家核销优惠券
export const shopHx = (data) => {
  const params = {
   url: '/screen/fkpt/tAjhFkptOrder/sjhx',
   method: 'POST',
   data,
  }
  return http(params)
}

// 商家查看核销记录和优惠券核销记录
export const shopHxjlAbdYhqjl = (data) => {
  const params = {
    url: '/screen/fkpt/tAjhFkptPayRecord/shopHxjlAbdYhqjl',
    method: 'GET',
    data,
  }
  return http(params)
}

// 商家核销金额
export const shopHxje = (data) => {
  const params = {
    url: '/screen/fkpt/tAjhFkptPayRecord/shopHxje',
    method: 'GET',
    data,
  }
  return http(params)
}
//历史核销记录
export const hxjlList = (data) => {
  const params = {
    url: '/screen/fkpt/tAjhFkptPayRecord/lshejelb',
    method: 'GET',
    data,
  }
  return http(params)
}

//饭卡平台-商家核销优惠券
export const shopHx2 = (data) => {
  const params = {
   url: '/screen/fkpt/tAjhFkptYhqUser/shopHx',
   method: 'POST',
   data,
  }
  return http(params)
}
//饭卡平台-商家核销订单
export const shopHx3 = (data) => {
  const params = {
   url: '/screen/fkpt/tAjhFkptGmsp/shopHx',
   method: 'POST',
   data,
  }
  return http(params)
}
