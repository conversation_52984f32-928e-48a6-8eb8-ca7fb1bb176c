<template>
  <view
    class="main"
    :style="{
      backgroundImage:
        'url(' + $getStaticImg('img/my/activity/disabled.png') + ')',
    }"
  >
    <view class="title">
      <view class="backIcon" @click="goBack">
        <u-icon name="arrow-left" color="#000" bold="true"></u-icon>
      </view>
      <view class="">
        {{ title }}
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      title: '',
    }
  },
  onLoad(option) {
    // if (option.title) {
    //   uni.setNavigationBarTitle({
    //     title: option.title,
    //   })
    // }
    this.title = option.title
  },
  methods: {
    goBack() {
      uni.navigateBack()
    },
  },
}
</script>

<style lang="scss" scoped>
.main {
  width: 100%;
  height: 100vh;
  background-size: 100% 100%;
  .title {
    height: 120rpx;
    line-height: 120rpx;
    font-size: 30rpx;
    font-weight: 600;
    text-align: center;
    /*  #ifdef  MP-WEIXIN */
    padding-top: 75rpx;
    /*  #endif  */
    /*  #ifdef  H5 */
    padding-top: 45rpx;
    /*  #endif  */

    .backIcon {
      position: absolute;
      /*  #ifdef  MP-WEIXIN */
      top: 110rpx;
      /*  #endif  */
      /*  #ifdef  H5 */
      top: 100rpx;
      /*  #endif  */
      left: 15rpx;
      color: #000;
      font-size: 16px;
      z-index: 999;
    }
  }
}
</style>
