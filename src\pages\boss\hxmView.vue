<!--
 * @Description: 
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2024-11-13 16:31:39
 * @LastEditors: wjb
 * @LastEditTime: 2024-11-15 11:48:57
-->
<template>
  <view>
    <canvas
      id="firstCanvas"
      style="width: 409px; height: 590px; position: fixed; left: 5000rpx"
      canvas-id="firstCanvas"
    />
    <!-- <image :src="hbImg" mode="widthFix" style="width: 409px; height: 590px" /> -->
  </view>
</template>

<script>
export default {
  props: {
    code: {
      type: [String, Number],
      default: '',
    },
    downloadFlag: {
      type: Boolean,
      default: false,
    },
  },
  watch: {
    downloadFlag: {
      handler(newVal, oldVal) {
        console.log(newVal, 'value变了')
        if (newVal != oldVal) {
          this.drawHaiBao()
        }
      },
    },
  },
  data() {
    return {
      ctx: null,
      config: {
        bgPath: this.$getStaticImg('img/yfk/login/sj_code_download.png'),
        bgWidth: 818,
        bgHeight: 1180,
      },
      hbImg: '',
    }
  },
  mounted() {},
  methods: {
    drawHaiBao() {
      let that = this
      this.ctx = uni.createCanvasContext('firstCanvas', that)
      console.log(this.ctx, 'this.ctx')
      this.drawBg()
    },
    drawBg() {
      const that = this
      uni.getImageInfo({
        src: that.config.bgPath,
        success(res) {
          console.log(res, 'success-res')
          that.ctx.drawImage(
            res.path,
            0,
            0,
            that.config.bgWidth * 0.5,
            that.config.bgHeight * 0.5
          )
          that.ctx.setFillStyle('#ffffff')
          that.getRrcode()
        },
      })
    },
    getRrcode() {
      let that = this
      uni.getImageInfo({
        src: that.code,
        success(res) {
          console.log(res, 'success-res')
          that.ctx.drawImage(res.path, 58, 190, 290, 290)
          console.log('11111', that.ctx)
          setTimeout(() => {
            that.ctx.draw(true, () => {
              // 3. canvas画布转成图片
              console.log(1111)
              uni.canvasToTempFilePath(
                {
                  x: 0,
                  y: 0,
                  width: that.config.bgWidth,
                  height: that.config.bgHeight,
                  destWidth: that.config.bgWidth,
                  destHeight: that.config.bgHeight,
                  canvasId: 'firstCanvas',
                  success: (res3) => {
                    console.log('通过画布绘制出的图片--保存的就是这个图', res3)
                    that.hbImg = res3.tempFilePath
                    that.saveHaiBao()
                  },
                  fail: (err) => {
                    console.log('失败。 。。。。。', err)
                  },
                },
                that
              )
            })
          }, 500)
        },
      })
    },
    saveHaiBao() {
      const imageUrl = this.hbImg
      // #ifdef MP-WEIXIN
      uni.saveImageToPhotosAlbum({
        filePath: imageUrl,
        success: (res2) => {
          console.log('成功', res2)
          uni.showToast({
            title: '下载成功~',
          })
        },
        fail: (err2) => {
          console.log('失败', err2)
            uni.showToast({
            title: '下载失败',
          })
        },
        complete: (res) => {
          console.log('完成', res)
        },
      })
      // #endif
      // #ifdef H5
      const a = document.createElement('a')
      a.href = imageUrl
      a.download = '图片'
      a.target = '_blank'
      a.click()
      // #endif
    },
  },
}
</script>

<style lang="scss" scoped>
</style>
