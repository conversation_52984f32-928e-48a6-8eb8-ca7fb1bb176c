import service from '@/api/service.js';

// import config from '@/mock/data/shouye.json'


// export function getAuthMenu(param) {
// 	return service.requestUrl(service.LOGINPATH + '/framework/dpcd/findSceMenu', 'GET', param)
// }

// 获取首页下面的菜单  本地
export function getMenuList() {
	// #ifdef APP-PLUS
	return new Promise((resolve, reject) => {
		if (config)
			resolve({data:config})
		else reject('请求失败')
	})
	// #endif
	// #ifndef APP-PLUS
	return service.requestUrl(service.DATABASEPATH + '/getMenu', 'POST')
	// return service.requestUrl(service.LOGINPATH + '/getMenu', 'POST')
	// #endif
}

// 获取首页下面的菜单  线上

export function getAuthMenu(params,header) {
	// #ifdef APP-PLUS
	return new Promise((resolve, reject) => {
		if (config)
			resolve({data:config})
		else reject('请求失败')
	})
	// #endif
	// #ifndef APP-PLUS
	return service.requestUrl(service.LOGINPATH + '/prod-api/screen/menu/findSceMenu', 'GET',params,header)

	// #endif
}

// 获取个人应用菜单 本地
export function getPersonalList() {
	// #ifdef APP-PLUS
	return new Promise((resolve, reject) => {
		if (config)
			resolve({data:config})
		else reject('请求失败')
	})
	// #endif
	// #ifndef APP-PLUS
	return service.requestUrl(service.DATABASEPATH + '/personal', 'POST')
	// #endif
}
// 获取个人应用菜单 线上
export function getPersonal(params,headers) {
	// #ifdef APP-PLUS
	return new Promise((resolve, reject) => {
		if (config)
			resolve({data:config})
		else reject('请求失败')
	})
	// #endif
	// #ifndef APP-PLUS
	return service.requestUrl(service.LOGINPATH + '/prod-api/screen/menu/findPerMenu', 'GET',params,headers)

	// #endif
}


// 获取我的菜单 线上
export function getMyService(params,headers) {
	// #ifdef APP-PLUS
	return new Promise((resolve, reject) => {
		if (config)
			resolve({data:config})
		else reject('请求失败')
	})
	// #endif
	// #ifndef APP-PLUS
	return service.requestUrl(service.LOGINPATH + '/prod-api/screen/menu/findMyMenu', 'GET',params,headers)

	// #endif
}

// 修改我的菜单 线上
export function updateMyService(params,headers) {
	// #ifdef APP-PLUS
	return new Promise((resolve, reject) => {
		if (config)
			resolve({data:config})
		else reject('请求失败')
	})
	// #endif
	// #ifndef APP-PLUS
	return service.requestUrl(service.LOGINPATH + '/prod-api/screen/menu/updateMyMenu', 'POST',params,headers)

	// #endif
}
// 获取大党办菜单 本地
export function getDdbMenuList() {
	// #ifdef APP-PLUS
	return new Promise((resolve, reject) => {
		if (config)
			resolve({data:config})
		else reject('请求失败')
	})
	// #endif
	// #ifndef APP-PLUS
	return service.requestUrl(service.DATABASEPATH + '/ddbMenu', 'GET')
	// #endif
}
