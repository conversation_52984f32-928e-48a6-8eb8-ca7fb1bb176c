<template>
  <view
    class="content"
    :style="{
      backgroundImage:
        'url(' + $getStaticImg('img/yfk/login/yfk_shd_bg-fk.png') + ')',
      backgroundSize: '100% auto',
      backgroundRepeat: 'no-repeat',
    }"
  >
    <view class="header-fixed flex-c">
      <view class="back-icon">
        <u-icon name="arrow-left" size="24" color="#fff" @tap="goBack"></u-icon>
      </view>
      <view class="title">统计</view>
    </view>
    <view class="main-wrap">
      <view class="hxBox">
        <view class="totalBox">
          <view>核销总金额</view>
          <view class="num1">
            {{ amount ? parseFloat(amount).toFixed(2) : 0 }}
          </view>
        </view>
        <view class="flex-b">
          <view class="hxBox2">
            <view>今日核销金额</view>
            <view class="num2">
              {{ todayAmount ? parseFloat(todayAmount).toFixed(2) : 0 }}
            </view>
          </view>
          <view class="hxBox2">
            <view>本月核销金额</view>
            <view class="num2">
              {{ monthAmount ? parseFloat(monthAmount).toFixed(2) : 0 }}
            </view>
          </view>
        </view>
      </view>
      <view class="flex-b">
        <view class="title">历史核销金额</view>
        <view class="container-time-hxjl flex-c" @click="show = true">
          <!-- <text v-if="!value2">
            {{ value1 }}
          </text>
          <text v-else>{{ value2 }}</text> -->
          <text>{{ year }}</text>
          <u-icon name="arrow-down" class="icon"></u-icon>
        </view>
      </view>
      <u-picker
        :show="show"
        :columns="[yearOptions]"
        @confirm="confirm"
        @close="show = false"
        :closeOnClickOverlay="true"
        :immediateChange="true"
      ></u-picker>

      <view class="list" v-if="list.length > 0">
        <view class="li" v-for="(item, i) in list" :key="i">
          <view class="line flex-b">
            <view class="label">时间</view>
            <view class="value">
              {{ item.time.slice(0, 4) + '年' + item.time.slice(5, 7) + '月' }}
            </view>
          </view>
          <view class="line flex-b">
            <view class="label">核销金额</view>
            <view class="value">
              {{ item.amount ? parseFloat(item.amount).toFixed(2) : 0 }}元
            </view>
          </view>
        </view>
      </view>
      <view class="emptyBox" v-else>
        <u-empty
          mode="data"
          icon="http://cdn.uviewui.com/uview/empty/data.png"
          text="暂无数据"
        ></u-empty>
      </view>
    </view>
    <view class="page-gray"></view>
  </view>
</template>

<script>
import { shopHxje, hxjlList } from '@/services/yfk/boss/hxjl.js'

export default {
  data() {
    return {
      amount: 0,
      todayAmount: 0,
      monthAmount: 0,

      year: '',
      yearOptions: [],
      show: false,
      list: [],
    }
  },
  onLoad() {
    const currentDate = new Date()
    const year = currentDate.getFullYear() // 获取年份，例如：2022
    this.year = year
    let startY = this.year - 20
    for (let i = this.year; i > startY; i--) {
      this.yearOptions.push(i)
    }

    this.getdata()
  },
  methods: {
    getdata() {
      shopHxje()
        .then((res) => {
          if (res.code === 200) {
            this.todayAmount = res.data.drhxje
            this.monthAmount = res.data.dyhxje
            this.amount = res.data.zhxje
          }
        })
        .catch(() => {})

      hxjlList({ cxsj: this.year }).then((res) => {
        this.list = res.data.map((item) => {
          return {
            time: item.sj,
            amount: item.hxje,
          }
        })
      })
    },
    // 关闭时间选择器
    cancal() {
      this.show = false
    },
    confirm(val) {
      console.log(val)
      this.year = val.value[0]

      this.getdata()
      this.show = false
    },
    goBack() {
      uni.navigateBack()
    },
  },
}
</script>

<style lang="scss" scoped>
.content {
  min-height: 100vh;
  padding: 100rpx 30rpx 40rpx 30rpx;
  box-sizing: border-box;
  font-family: Source Han Sans, Source Han Sans;
  .header-fixed {
    position: fixed;
    z-index: 50;
    left: 0;
    right: 0;
    top: 0;
    height: 88rpx;
    width: 100%;
    padding-top: 88rpx;
    .back-icon {
      position: absolute;
      left: 30rpx;
      top: 100rpx;
      // margin-left: 30rpx;
    }
    .title {
      width: 100%;
      font-size: 36rpx;
      font-weight: 500;
      color: #fff;
      text-align: center;
      // font-family: 'ASHT-B';
    }
  }
  .main-wrap {
    margin-top: 100rpx;
  }
  .hxBox {
    padding: 40rpx 60rpx 54rpx 60rpx;
    box-sizing: border-box;
    background-color: #fff;
    border-radius: 20rpx;
    font-family: Source Han Sans, Source Han Sans;
    font-size: 32rpx;
    color: #999999;
    margin-bottom: 54rpx;
    .totalBox,
    .hxBox2 {
      display: flex;
      align-items: center;
      flex-direction: column;
      justify-content: center;
    }
    .num1 {
      margin-top: 10rpx;
      margin-bottom: 40rpx;
      font-family: DIN, DIN;
      font-weight: 500;
      font-size: 72rpx;
      color: #191919;
    }
    .num2 {
      margin-top: 20rpx;
      font-family: DINA, DINA;
      font-size: 60rpx;
      color: #191919;
    }
  }
  .list {
    padding: 20rpx 0;
    .li {
      padding: 30rpx 30rpx;
      box-sizing: border-box;
      background-color: #fff;
      border-radius: 20rpx;
      margin-bottom: 20rpx;
      .line {
        margin-bottom: 24rpx;
        &:last-child {
          margin-bottom: 0;
        }
        .label {
          font-size: 28rpx;
          color: #999999;
        }
        .value {
          font-size: 28rpx;
          color: #191919;
        }
      }
    }
  }
  .container-time-hxjl {
    width: 134rpx;
    height: 60rpx;
    line-height: 60rpx;
    padding-left: 24rpx;
    background: #ffffff;
    margin-left: 30rpx;
    border-radius: 20rpx;
    font-weight: 400;
    font-size: 26rpx;
    font-style: normal;
    text-transform: none;

    text {
      float: left;
    }

    image {
      float: right;
      width: 22rpx;
      height: 13rpx;
      text-align: right;
      margin-right: 16rpx;
      margin-top: 24rpx;
    }
    ::v-deep .u-icon {
      margin-left: 18rpx !important;
    }
  }
  .emptyBox {
    font-size: 32rpx;
    color: #1d2129;
    line-height: 46rpx;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    .empty-img {
      width: 358rpx;
      height: 268rpx;
      margin: 0rpx 0 60rpx 0;
    }
  }
}
.flex-c {
  display: flex;
  align-items: center;
}
.flex-b {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
