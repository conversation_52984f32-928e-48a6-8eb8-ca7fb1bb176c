<template>
  <view style="min-height: 100vh; background: #eef1f4; padding-top: 1rpx">
    <view class="tips">选择您要核销的{{cardType == 3?'次卡':'时间卡'}}</view>
    <view v-if="records && records.length > 0">
      <view
        class="list-item"
        v-for="(item, key) in records"
        :key="key"
        @click="selectCard(item)"
      >
        <view class="name_box">
          <image
            class="pro-ico"
            :src="$getStaticImg('img/yfk/login/ico8.png')"
          />
          <view class="name">
            <view class="dp_name">{{ item.spmc }}</view>
          </view>
        </view>
        <view class="number" v-if="cardType == 3">
          <view class="count">{{ item.residueNum }}</view>
          <view class="unit">剩余次数</view>
        </view>
        <view class="number" v-if="cardType == 2">
          <view class="count">{{ item.endTime }}</view>
          <view class="unit">有效期</view>
        </view>
      </view>
    </view>
    <u-empty
      v-else
      :icon="$getStaticImg('img/fyk/login/nodata.png')"
      text="暂无数据"
    />
  </view>
</template>

<script>
import { getCardInfo } from '@/services/yfk/boss/hxjl.js'
import { sendXHJE } from '@/services/yfk/myOrder'
export default {
  data() {
    return {
      userId: '',
      cardType: '',
      code: '',
      records: [],
    }
  },
  onLoad(data) {
    this.userId = JSON.parse(decodeURIComponent(data.codedata)).userId
    this.cardType = JSON.parse(decodeURIComponent(data.codedata)).hxType
    this.code = JSON.parse(decodeURIComponent(data.codedata)).code
    this.getList()
  },
  methods: {
    // 核销时商户根据用户id获取卡信息
    getList() {
      getCardInfo({ userId: this.userId, cardType: this.cardType })
        .then((res) => {
          if (res.code === 200) {
            this.records = res.data
          }
        })
        .catch(() => {})
    },
    selectCard(item) {
      sendXHJE({
        code: this.code,
        hxType: this.cardType,
        cardId: item.cardId,
      }).then((res) => {
        if (res.code == 200) {
          uni.redirectTo({
            url:
              '/pages/boss/hxjg?orderId=' +
              encodeURIComponent(JSON.stringify(res.data)),
          })
        } else {
          uni.showToast({
            title: res.msg,
            position: 'bottom',
            duration: 3000,
          })
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.tips {
  font-family: Source Han Sans, Source Han Sans;
  font-weight: 400;
  font-size: 36rpx;
  color: #191919;
  line-height: 43rpx;
  margin: 30rpx;
}
.list-item {
  margin: 20rpx 30rpx;
  padding: 24rpx 30rpx;
  background: #ffffff;
  border-radius: 20rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  align-content: center;
  justify-content: space-between;

  .name_box {
    display: flex;
    align-items: center;
    align-content: center;
    .pro-ico {
      margin-right: 30rpx;
      width: 80rpx;
      height: 80rpx;
    }

    .name {
      .dp_name {
        font-family: Source Han Sans, Source Han Sans;
        font-weight: 400;
        font-size: 36rpx;
        color: #191919;
        line-height: 52rpx;
      }
      .sp_name {
        font-family: Source Han Sans, Source Han Sans;
        font-weight: 400;
        font-size: 24rpx;
        color: #999999;
        line-height: 35rpx;
        margin-top: 2rpx;
      }
    }
  }

  .number {
    text-align: center;
    .count {
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 700;
      font-size: 40rpx;
      color: #191919;
      line-height: 58rpx;
    }

    .unit {
      font-family: Source Han Sans, Source Han Sans;
      font-size: 28rpx;
      color: #999;
      line-height: 32rpx;
    }
  }
}
</style>
