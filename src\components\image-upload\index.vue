<template>
  <u-upload
    :file-list="fileList"
    :max-count="maxCount"
    :preview-full-image="previewImage"
    width="100"
    height="100"
    :multiple="multiple"
    :max-size="maxSize * 1024 * 1024"
    :disabled="disabled"
    @afterRead="afterRead"
    @delete="deletePic"
    @oversize="oversize"
  >
    <slot>
      <view class="default-upload-wrap flex-c-c">
        <u-icon name="camera-fill" color="#dcdee0" size="28"></u-icon>
      </view>
    </slot>
  </u-upload>
</template>

<script>
import { uploadHttp } from '@/utils/http'
export default {
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    maxCount: {
      type: Number,
      default: 3,
    },
    maxSize: {
      type: Number,
      default: 10,
    },
    previewImage: {
      type: Boolean,
      default: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    multiple: {
      type: <PERSON><PERSON>an,
      default: false,
    },
  },
  data() {
    return {
      fileList: [],
    }
  },
  watch: {
    fileList: {
      handler(newName, oldName) {
        console.log(newName)
      },
      // immediate: true,
      deep: true,
    },
    value: {
      handler(newVal, oldVal) {
        this.fileList = newVal
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    afterRead(e) {
      console.log(e, 'afterRead')
      if (!this.multiple) {
        this.fileList.push({
          ...e.file,
          status: 'uploading',
          message: '上传中',
        })
        console.log(this.fileList)
        uploadHttp(e.file.url).then((res) => {
          console.log(res, 'uploadHttp')
          if (res.code === 200) {
            const d = res.result
            const index = this.fileList.findIndex((a) => a.url === e.file.url)
            console.log(index)
            this.$set(this.fileList, index, {
              ...e.file,
              url: d.proxyPath + d.relativePath,
              result: d,
              status: 'success',
              message: '',
            })
            console.log(this.fileList)
            this.$emit('input', this.fileList)
          }
        })
      } else {
        let fileList2 = []
        let length = this.fileList.length //记录已上传的图片的长度
        e.file.forEach((item) => {
          fileList2.push({
            file: item,
            status: 'uploading',
            message: '上传中',
          })
        })
        console.log(fileList2)
        fileList2.forEach((item, index) => {
          uploadHttp(item.file.url).then((res) => {
            console.log(res, 'uploadHttp')
            if (res.code === 200) {
              const d = res.result
              this.$set(this.fileList, length + index, {
                ...item.file,
                url: d.proxyPath + d.relativePath,
                result: d,
                status: 'success',
                message: '',
              })
              console.log(this.fileList)
              this.$emit('input', this.fileList)
            }
          })
        })
      }
    },
    deletePic(e) {
      console.log(e, 'deletePic')
      const index = this.fileList.findIndex((f) => f.url === e.file.url)
      this.fileList.splice(index, 1)
      this.$emit('input', this.fileList)
    },
    oversize(file) {
      console.log(file, 'oversize')
      uni.showToast({
        icon: 'none',
        title: `图片最大不能超过${this.maxSize}MB`,
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.default-upload-wrap {
  width: 200rpx;
  height: 200rpx;
  background: #f4f5f7;
}
</style>
