<template>
  <view class="content">
    <!-- <view v-if="extra.type == 'iframe'" class="pad-iframe">
      <web-view
        :ref="extra.key ? extra.key : extra.title"
        class="pad-iframe"
        :src="pageUrl"
      ></web-view>
    </view> -->
    <view v-if="extra.type == 'iframe'" class="pad-iframe">
      <!-- #ifdef MP-WEIXIN -->
      <web-view  :ref="extra.key ? extra.key : extra.title" class="pad-iframe"
        :src="pageUrl" ></web-view>

      <!-- #endif -->
      <!-- #ifdef H5 -->
      <iframe :ref="extra.key ? extra.key : extra.title" class="pad-iframe" id="iframe"
        :src="pageUrl"></iframe>
      <!-- #endif -->
    </view>
<!-- http://************:3333/screen/ -->
  </view>
</template>

<script>
// import CommonPage from '@/pages/common/common-page.vue'
export default {
  // components: {
  //   CommonPage,
  // },
  data() {
    return {
      extra: {},
      pageUrl: '',
      iframe_content: null
    }
  },
  mounted() {
    // #ifdef H5
    this.iframe_content = document.getElementById('iframe')
    // #endif
  },
  methods: {
    addEvents() {
      // #ifdef H5
      window.addEventListener('message', (event) => {
        console.log(event);
        if (!this.iframe_content.contentWindow) return

        switch (event.data.cmd){
          case 'getScanCodes':
            this.iframe_content.contentWindow.postMessage({
              cmd: 'getScanCodes',
              value: event.data.value
            }, '*')
            break;
            case 'getPosition':
              this.iframe_content.contentWindow.postMessage({
                cmd: 'getPosition',
                value: event.data.value
              }, '*')
              break;
              case 'setTitle':
               uni.setNavigationBarTitle({
                 title:event.data.title
               })
                break;
          default:
            break;
        }

      })
      // #endif
    }
  },
  onLoad(option) {
    this.addEvents()
   
    // console.log(option.extra)
    this.extra = option.extra
      ? JSON.parse(decodeURIComponent(option.extra))
      : null
    console.log(this.extra)
    if (this.extra?.title) {
      uni.setNavigationBarTitle({
        title: this.extra.title,
      })
    }
    if (this.extra?.extra) {
      const extra = this.extra.extra
        ? '?extra=' + encodeURIComponent(JSON.stringify(this.extra.extra))
        : ''
      this.pageUrl = this.extra.url ? this.extra.url + extra : ''
    } else {
      this.pageUrl = this.extra?.url ?? ''
    }
  },
}
</script>

<style>
.pad-iframe {
  width: 100%;
  height: 100%;
  min-height: 100vh;
  border: unset;
}

.content {
  padding: 0 !important;
}
</style>
