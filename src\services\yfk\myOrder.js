import { http } from '@/utils/http'

// 订单列表
export const orderList = (data) => {
  const params = {
    url: '/screen/yfk/ajhYfkOrder/myOrder',
    method: 'GET',
    data,
  }
  return http(params)
}

/*
    我的预付卡
*/
// 生成支付二维码所需的url
export const getCodeText = (data) => {
  const params = {
    url: '/screen/yfk/base/creatPayUrl',
    method: 'GET',
    data,
  }
  return http(params)
}
// 生成支付二维码
export const getPayCode = (data) => {
  const params = {
    url: '/system/common/generate-qrCode',
    method: 'GET',
    data,
  }
  return http(params)
}

// 通用生成二维码请求阿里云
export const getCodeForUrl = (data) => {
  const params = {
    url: '/screen/twoCode/generateQrCode',
    method: 'GET',
    data,
  }
  return http(params)
}

// 用户卡包接口
export const getShopData = (data) => {
  const params = {
    url: '/screen/yfk/ajhYfkCard/userCard',
    method: 'GET',
    data,
  }
  return http(params)
}

// 用户获取店铺优惠券列表(移动端)
export const getMyYhq = (data) => {
  const params = {
    url: '/screen/yfk/tAjhYfkYhqgl/myYhq',
    method: 'GET',
    data,
  }
  return http(params)
}

//优惠券核销码字符串
export const creatYhqString = (data) => {
  const params = {
    url: '/screen/yfk/base/creatYhqString',
    method: 'GET',
    data,
  }
  return http(params)
}

// 用户扫码后核销
export const sendUserSjhx = (data) => {
  const params = {
    url: '/screen/yfk/ajhYfkOrder/userSjhx',
    method: 'POST',
    data,
  }
  return http(params)
}

/*
    商户端
*/
// 核销金额
export const sendXHJE = (data) => {
  const params = {
    url: '/screen/yfk/ajhYfkOrder/sjhx',
    method: 'POST',
    data,
  }
  return http(params)
}
// 我的订单----取消订单
export const calOrder = (data) => {
  const params = {
    url: '/screen/yfk/ajhYfkOrder/cancelOrder',
    method: 'POST',
    data,
  }
  return http(params)
}
// 我的订单----订单详情
export const orderDetails = (data) => {
  const params = {
    url: `/screen/fkpt/tAjhFkptOrder/${data}`,
    method: 'GET',
  }
  return http(params)
}
