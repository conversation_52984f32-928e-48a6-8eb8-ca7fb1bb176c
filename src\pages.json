{
  "easycom": {
    "^u-(.*)": "uview-ui/components/u-$1/u-$1.vue"
  },
  "pages": [
    //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
    //预付卡模块
    // 商户端登录-预付卡登录
    {
      "path": "pages/boss/index",
      "style": {
        "navigationBarTitleText": "商户端首页",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/yfk_login/pwd",
      "style": {
        "navigationBarTitleText": "密码登录",
        "navigationBarBackgroundColor": "#FFFFFF"
      }
    },
    {
      "path": "pages/yfk_login/login",
      "style": {
        "navigationBarTitleText": "登录",
        "navigationStyle": "custom",
        "navigationBarBackgroundColor": "#FFFFFF"
      }
    },
    {
      "path": "pages/boss/smhx",
      "style": {
        "navigationBarTitleText": "核销",
        "navigationBarBackgroundColor": "#ffffff"
      }
    },
    {
      "path": "pages/boss/hxjg",
      "style": {
        "navigationBarTitleText": "核销记录",
        "navigationBarBackgroundColor": "#EEF1F4"
      }
    },
    {
      "path": "pages/boss/hxjg-xj",
      "style": {
        "navigationBarTitleText": "核销记录",
        "navigationBarBackgroundColor": "#EEF1F4"
      }
    },
    {
      "path": "pages/boss/coupon",
      "style": {
        "navigationBarTitleText": "优惠券核销",
        "navigationBarBackgroundColor": "#FFFFFF"
      }
    },
    {
      "path": "pages/boss/hxjl",
      "style": {
        "enablePullDownRefresh": true,
        "navigationBarTitleText": "核销记录"
      }
    },
    {
      "path": "pages/boss/hx-select",
      "style": {
        "enablePullDownRefresh": true,
        "navigationBarTitleText": "选择"
      }
    },
    {
      "path": "pages/boss/hxm",
      "style": {
        "enablePullDownRefresh": true,
        "navigationBarTitleText": "核销码"
      }
    },
    {
      "path": "pages/boss/statistics",
      "style": {
        "enablePullDownRefresh": true,
        "navigationStyle": "custom",
        "navigationBarTitleText": "统计"
      }
    },
    {
      "path": "pages/demo/index",
      "style": {
        "enablePullDownRefresh": true,
        "navigationBarTitleText": ""
      }
    },
    {
      "path": "pages/demo/index2",
      "style": {
        "enablePullDownRefresh": true,
        "navigationBarTitleText": ""
      }
    }
  ],
  "subPackages": [],
  "globalStyle": {
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "爱金华",
    "navigationBarBackgroundColor": "#F8F8F8",
    "backgroundColor": "#F8F8F8"
    // "navigationBarBackgroundImage": "/static/officeImg/bgOne.png"
    // "titleNView":{      //原生导航栏配置参数
    //           "color":"#FF0000",     //设置背景图
    //          }
  },
  "tabBar": {
    "custom": true,
    "color": "#191919",
    "backgroundColor": "#ffffff",
    "height": "120",
    "iconWidth": "22px",
    "selectedColor": "#000000",
    "list": [
      {
        "pagePath": "pages/demo/index",
        "text": "主页",
        "iconPath": "static/img/tabbar/home.png",
        "selectedIconPath": "static/img/tabbar/home-active.png"
      },
      {
        "pagePath": "pages/demo/index2",
        "text": "我的",
        "iconPath": "static/img/tabbar/wd.png",
        "selectedIconPath": "static/img/tabbar/wd-active.png"
      }
    ]
  },
  "condition": {
    //模式配置，仅开发期间生效
    "current": 0, //当前激活的模式(list 的索引项)
    "list": [
      {
        "name": "", //模式名称
        "path": "pages/yfk_login/index",
        "query": "" //启动参数，在页面的onLoad函数里面得到
      }
    ]
  }
}
