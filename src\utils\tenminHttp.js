// 搬迁八婺15分钟生活圈api相关

import store from '@/store/index'

const BASE_URL = process.env.VUE_APP_TENMIN_BASE_URL
export const tenminHttp = (options) => {
  return new Promise((resolve, reject) => {
    uni.request({
      url: BASE_URL + options.url,
      method: options.method || 'GET',
      data: options.data || {},
      header: { Authorization: '' },
      success: (res) => {
        if (res.statusCode === 200) {
          if (res.data.code === 401) {
            resolve(res.data)
            store.commit('set_props', {
              token: '',
            })
            // #ifdef H5
            uni.navigateTo({
              url: '/pages/my/yfk_login/index',
            })
            // #endif
            // #ifdef MP-WEIXIN
            uni.navigateTo({
              url: '/pages/my/login/wxLogin',
            })
            // #endif
          } else {
            resolve(res.data)
          }
        }
      },
      fail: (err) => {
        uni.showToast({
          icon: 'none',
          title: `${BASE_URL + options.url}请求失败`,
        })
        reject(err)
      },
    })
  })
}
