<!--
 * @Description: 
 * @Version: 1.0
 * @Autor: wjb
 * @Date: 2024-12-02 14:06:43
 * @LastEditors: wjb
 * @LastEditTime: 2025-01-23 09:01:49
-->
<template>
  <view>
    <view class="page-green"></view>
    <view class="tab_relative">
      <view class="main-wrap">
        <view class="title">客户扫码即可核销预付卡</view>
        <image class="ewm" :src="payCode" />
        <view class="refresh-container" @click="downloadEwm">
          <image :src="$getStaticImg('img/yfk/login/ewm_download.png')" />
          <text>下载二维码</text>
        </view>
      </view>
    </view>
    <hxmView :code="payCode" :downloadFlag='downloadFlag'></hxmView>
  </view>
</template>

<script>
import { getCodeForUrl } from '@/services/yfk/myOrder'
import hxmView from './hxmView.vue'
export default {
  data() {
    return {
      // 二维码
      payCode: '',
      //下载
      downloadFlag: false,
    }
  },
  components: { hxmView },
  computed: {
    userId() {
      return this.$store.state.userId
    },
  },
  methods: {
    downloadEwm() {
      this.downloadFlag = !this.downloadFlag
    },
    // 获取支付二维码
    getCodeForUrl() {
      getCodeForUrl({
        url:
          'https://www.aijinhua.cn:8443/fileServer/ajhApplet/static/files/diningCard/yhsmhx?type=cyhxm&id=' +
          this.userId, // 扫码核销码
        width: 200,
        height: 200,
      }).then((res) => {
        if (res.code == 200) {
          this.payCode = res.data
        }
      })
    },
    // 初始化
    init() {
      this.getCodeForUrl()
    },
  },
  onPullDownRefresh() {},
  onReachBottom() {},
  onLoad() {
    this.init()
  },
}
</script>

<style lang="scss" scoped>
.page-green {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #FF3C00;
  z-index: -1;
}
.tab_relative {
  position: relative;
  padding-top: 30rpx;

  .main-wrap {
    position: relative;
    margin: 0 30rpx;
    z-index: 2;
    width: 690rpx;
    height: 800rpx;
    border-radius: 20rpx;
    box-sizing: border-box;
    background: #ffffff;

    .title {
      font-family: Source Han Sans, Source Han Sans;
      font-weight: 500;
      font-size: 32rpx;
      color: #000000;
      line-height: 44rpx;
      text-align: center;
      padding-top: 112rpx;
    }

    .ewm {
      margin: 40rpx auto 30rpx;
      display: block;
      width: 400rpx;
      height: 400rpx;
    }

    .refresh-container {
      margin: 0 auto;
      width: 220rpx;
      margin-top: 64rpx;

      image {
        margin-right: 12rpx;
        display: inline-block;
        width: 24rpx;
        height: 24rpx;
        vertical-align: middle;
        transition: transform 0.5s ease;
      }

      text {
        display: inline-block;
        vertical-align: middle;
        color: #0084ff;
        font-size: 30rpx;
        font-family: 'Source Han Sans-Regular';
        font-weight: 400;
      }
    }
  }
}
</style>
