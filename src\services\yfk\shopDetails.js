import { http } from '@/utils/http'
// 店铺详情信息
export const yfkshopDetails = (data) => {
  const params = {
    url: '/screen/yfk/ajhYfkShgl/details',
    method: 'GET',
    data
  }
  return http(params)
}
// 店铺首页商品列表
export const yfkGoodsList = (data) => {
  const params = {
    url: '/screen/yfk/ajhYfkSpgl/homePagelist',
    method: 'GET',
    data,
  }
  return http(params)
}

// 店铺列表

export const yfkproList = (data) => {
  const params = {
    url: '/screen/yfk/ajhYfkShgl/list',
    method: 'GET',
    data,
  }
  return http(params)
}
//店铺优惠券列表
export const getCouponsList = (data) => {
  const params = {
    url: '/screen/yfk/tAjhYfkYhqgl/getdDpyhqUser',
    method: 'GET',
    data,
  }
  return http(params)
}
//用户抢券
export const userGetCoupon = (data) => {
  const params = {
   url: '/screen/yfk/tAjhYfkYhqgl/userGrabTickets',
   method: 'POST',
   data,
  }
  return http(params)
 }
