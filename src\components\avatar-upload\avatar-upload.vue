<template>
  <view class="common-upload">
    <view class="image-list">
      <view class="image-item" :style="itemStyle" @tap="UploadTap">
        <image class="image" mode="aspectFill" :src="image.url" />
      </view>
    </view>
  </view>
</template>

<script>
import { uploadHttp } from '@/utils/http'
export default {
  name: 'CommonUpload',
  props: {
    value: {
      type: Object,
      default: () => {},
    },
    maxCount: {
      type: Number,
      default: 1,
    },
    maxSize: {
      type: Number,
      default: 10,
    },
    itemStyle: {
      type: Object,
      default: () => {
        return {
          width: '100rpx',
          height: '100rpx',
          borderRadius: '50%',
        }
      },
    },
  },
  data() {
    return {
      timeStamp: new Date().getTime(),
      image: { url: '' },
    }
  },
  watch: {
    image: {
      handler(newName, oldName) {
        console.log(newName)
      },
      // immediate: true,
      deep: true,
    },
    value: {
      handler(newVal, oldVal) {
        this.image.url = newVal
      },
      immediate: true,
      deep: true,
    },
  },
  mounted() {
    window.addEventListener('message', (event) => {
      // console.log(event, 'event-message')
      if (
        event.data &&
        event.data.cmd == 'getAppUploadData' &&
        event.data.value &&
        event.data.value.type === this.timeStamp
      ) {
        console.log(event.data, 'event.data-getAppUploadData')
        this.doUpload(event.data)
      }
    })
  },
  methods: {
    UploadTap() {
      console.log('UploadTap')
      const that = this
      uni.showActionSheet({
        itemList: ['拍摄', '从相册选择'],
        success: function (res) {
          console.log('选中了第' + (res.tapIndex + 1) + '个按钮')
          if (res.tapIndex === 0) {
            window.parent.postMessage(
              {
                cmd: 'getAppCameraUpload',
                options: {
                  type: that.timeStamp,
                  count: 1,
                },
              },
              '*'
            )
          } else if (res.tapIndex === 1) {
            window.parent.postMessage(
              {
                cmd: 'getAppPhotoUpload',
                options: {
                  type: that.timeStamp,
                  count: 1,
                },
              },
              '*'
            )
          }
        },
        fail: function (res) {
          console.log(res.errMsg)
        },
      })
    },
    doUpload(v) {
      console.log(v, 'doUpload')
      if (v && v.value && v.value.errMsg === 'chooseImage:ok') {
        const tepArr = v.value.tempFiles
        const blodArr = tepArr.map((t) => {
          const blob = this.dataURLtoBlob(t.path)
          const blobUrl = URL.createObjectURL(blob)
          console.log('1111', blobUrl)
          return {
            ...t,
            path: blobUrl,
          }
        })
        console.log(blodArr, 'blodArr')
        this.onBeforeRead(blodArr)
      }
    },
    /**
     * Base64字符串转二进制流
     * @param {String} dataurl Base64字符串(字符串包含Data URI scheme，例如：data:image/png;base64, )
     */
    dataURLtoBlob(dataurl) {
      var arr = dataurl.split(',')
      var mime = arr[0].match(/:(.*?);/)[1]
      var bstr = atob(arr[1])
      var n = bstr.length
      var u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }
      console.log(mime, 'mine-mine-type')
      return new Blob([u8arr], {
        type: mime,
      })
    },
    onBeforeRead(v) {
      const d = v.some((item) => item.size > this.maxSize * 1024 * 1024)
      if (d) {
        uni.showToast({
          icon: 'none',
          title: `图片大小不能超过${this.maxSize}M`,
        })
      } else {
        this.onAfterRead(v)
      }
    },
    onAfterRead(v) {
      if (!v.length) {
        return
      }
      uploadHttp(v[0].path, { fileName: v[0].name })
        .then((res) => {
          console.log(res, 'uploadHttp')
          if (res.code === 200) {
            const d = res.result
            this.image = {
              file: v[0].path,
              url: d.proxyPath + d.relativePath,
              result: d,
              status: 'success',
              message: '',
            }
            this.$emit('input', this.image)
          } else {
          }
        })
        .catch((err) => {
          console.log(err, 'uploadHttp-catch')
        })
    },
  },
}
</script>

<style lang="scss" scoped>
.common-upload {
  display: inline-block;
  .image-list {
    display: flex;
    flex-wrap: wrap;
    .image-item {
      position: relative;
      width: 100rpx;
      height: 100rpx;
      background: #f4f5f7;
      border-radius: 50%;
      overflow: hidden;
      .image {
        width: 100%;
        height: 100%;
      }
      .del-btn {
        position: absolute;
        top: 0;
        right: 0;
        background-color: #373737;
        height: 28rpx;
        width: 28rpx;
        display: flex;
        flex-direction: row;
        border-bottom-left-radius: 100px;
        align-items: center;
        justify-content: center;
        z-index: 3;
        .del-btn-icon {
          margin-top: -14rpx;
          margin-right: -6rpx;
          color: #fff;
          font-size: 24rpx;
        }
      }
    }
  }
}
</style>
