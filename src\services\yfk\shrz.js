import { http } from '@/utils/http'

//判断是否已提交商户入驻申请
export const getShrzStatus = (data) => {
    const params = {
     url: '/screen/yfk/ajhYfkShgl/getShrzzt',
     method: 'GET',
     data,
    }
    return http(params)
}

// 行业数据
export const getFieldsData = (data) => {
    const params = {
     url: '/screen/yfk/ajhYfkShgl/getSpSshy',
     method: 'GET',
     data,
    }
    return http(params)
}

//新增商户入驻
export const addShrz = (data) => {
  const params = {
   url: '/screen/yfk/ajhYfkShgl/add',
   method: 'POST',
   data,
  }
  return http(params)
 }
