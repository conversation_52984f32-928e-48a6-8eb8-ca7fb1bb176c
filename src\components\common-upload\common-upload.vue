<template>
  <view class="common-upload">
    <view class="image-list">
      <!-- {{ imageList.length > 0 ? imageList[0].path : '' }} -->
      <view
        v-for="(item, index) of imageList"
        :key="item.url"
        class="image-item"
        :style="itemStyle"
        @click="doImagePerview(index)"
      >
        <image class="image" mode="aspectFill" :src="item.url" />
        <view v-if="delAble" class="del-btn" @click.stop="deletePic(item.url)">
          <text class="del-btn-icon">x</text>
        </view>
      </view>
      <view @tap="UploadTap">
        <slot>
          <view
            v-if="imageList.length < maxCount"
            class="default-upload-wrap image-item flex-c-c"
            :style="itemStyle"
          >
            <u-icon name="camera-fill" color="#dcdee0" size="28"></u-icon>
          </view>
        </slot>
      </view>
    </view>
  </view>
</template>

<script>
import { uploadHttp } from '@/utils/http'
export default {
  name: 'CommonUpload',
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    maxCount: {
      type: Number,
      default: 3,
    },
    maxSize: {
      type: Number,
      default: 10,
    },
    previewImage: {
      type: Boolean,
      default: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    delAble: {
      type: Boolean,
      default: true,
    },
    itemStyle: {
      type: Object,
      default: () => {
        return {
          width: '160rpx',
          height: '160rpx',
          margin: '0 20rpx 20rpx 0',
        }
      },
    },
  },
  data() {
    return {
      timeStamp: new Date().getTime(),
      imageList: [
        // { url: 'https://cdn.uviewui.com/uview/swiper/1.jpg' },
        // { url: 'https://cdn.uviewui.com/uview/swiper/2.jpg' },
        // { url: 'https://cdn.uviewui.com/uview/swiper/3.jpg' },
      ],
      // maxSize: 5, // 限制图片大小多少M
    }
  },
  watch: {
    imageList: {
      handler(newName, oldName) {
        console.log(newName)
      },
      // immediate: true,
      deep: true,
    },
    value: {
      handler(newVal, oldVal) {
        this.imageList = newVal
      },
      immediate: true,
      deep: true,
    },
  },
  mounted() {
    // const that = this
    window.addEventListener('message', (event) => {
      // console.log(event, 'event-message')
      if (
        event.data &&
        event.data.cmd == 'getAppUploadData' &&
        event.data.value &&
        event.data.value.type === this.timeStamp
      ) {
        console.log(event.data, 'event.data-getAppUploadData')
        this.doUpload(event.data)
      }
    })
  },
  methods: {
    UploadTap() {
      console.log('UploadTap')
      const that = this
      uni.showActionSheet({
        itemList: ['拍摄', '从相册选择'],
        success: function (res) {
          console.log('选中了第' + (res.tapIndex + 1) + '个按钮')
          if (res.tapIndex === 0) {
            window.parent.postMessage(
              {
                cmd: 'getAppCameraUpload',
                options: {
                  type: that.timeStamp,
                  count: that.maxCount - that.imageList.length,
                },
              },
              '*'
            )
          } else if (res.tapIndex === 1) {
            window.parent.postMessage(
              {
                cmd: 'getAppPhotoUpload',
                options: {
                  type: that.timeStamp,
                  count: that.maxCount - that.imageList.length,
                },
              },
              '*'
            )
          }
        },
        fail: function (res) {
          console.log(res.errMsg)
        },
      })
    },
    doUpload(v) {
      console.log(v, 'doUpload')
      if (v && v.value && v.value.errMsg === 'chooseImage:ok') {
        const tepArr = v.value.tempFiles
        const blodArr = tepArr.map((t) => {
          const blob = this.dataURLtoBlob(t.path)
          const blobUrl = URL.createObjectURL(blob)
          console.log('1111', blobUrl)
          return {
            ...t,
            path: blobUrl,
          }
        })
        console.log(blodArr, 'blodArr')
        // let blob = this.dataURLtoBlob(tepArr[0].path)
        // var blobUrl = URL.createObjectURL(blob);
        // tepArr[0].path=blobUrl
        // console.log('1111', blobUrl)
        this.onBeforeRead(blodArr)
      }
    },
    /**
     * Base64字符串转二进制流
     * @param {String} dataurl Base64字符串(字符串包含Data URI scheme，例如：data:image/png;base64, )
     */
    dataURLtoBlob(dataurl) {
      var arr = dataurl.split(',')
      var mime = arr[0].match(/:(.*?);/)[1]
      var bstr = atob(arr[1])
      var n = bstr.length
      var u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }
      console.log(mime, 'mine-mine-type')
      return new Blob([u8arr], {
        type: mime,
      })
    },
    onBeforeRead(v) {
      const d = v.some((item) => item.size > this.maxSize * 1024 * 1024)
      if (d) {
        uni.showToast({
          icon: 'none',
          title: `图片大小不能超过${this.maxSize}M`,
        })
      } else {
        // 临时路径转换为file对象
        // 使用uni.getFileSystemManager获取文件系统管理器
        // const fileSystemManager = uni.getFileSystemManager()
        // // 遍历图片路径数组
        // v.forEach((item, index) => {
        //   // 在这里将路径转换为File对象
        //   fileSystemManager.readFile({
        //     filePath: item.path, // 图片文件的临时路径
        //     success: function (res) {
        //       // 创建File对象
        //       const file = new File([res.data], 'image' + index, {
        //         type: 'image/png', // 假设图片格式为png，根据实际情况修改
        //       })
        //       // 在这里可以使用file对象了
        //       console.log(file, 'file-file')
        //     },
        //     fail: function (error) {
        //       console.error('读取文件失败:', error)
        //     },
        //   })
        // })
        // v.forEach((item, index) => {
        //   uni.getImageInfo({
        //     src: item.path,
        //     success: (res) => {
        //       console.log(res, 'getImageInfo')
        //     },
        //     fail: (err) => {
        //       console.error(err)
        //     },
        //   })
        // })
        this.onAfterRead(v)
        // const that = this
        // v.forEach((item) => {
        //   uploadHttp(item).then((res) => {
        //     console.log(res, '上传成功')
        //     that.imageList.push(res.data)
        //   })
        // })
      }
    },
    onAfterRead(v) {
      console.log(v, 'onAfterRead')
      // this.imageList.push(v[0])
      v.forEach((item, index) => {
        // this.imageList.push({
        //   file: item.path,
        //   status: 'uploading',
        //   message: '上传中',
        // })
        uploadHttp(item.path, { fileName: item.name })
          .then((res) => {
            console.log(res, 'uploadHttp')
            if (res.code === 200) {
              const d = res.result
              // this.$set(this.imageList, index, {
              //   file: item.path,
              //   url: d.proxyPath + d.relativePath,
              //   result: d,
              //   status: 'success',
              //   message: '',
              // })
              this.imageList.push({
                file: item.path,
                url: d.proxyPath + d.relativePath,
                result: d,
                status: 'success',
                message: '',
              })
              console.log(this.imageList, 'this.imageList')
              this.$emit('input', this.imageList)
              // this.$emit('input', this.fileList)
            } else {
              // this.$set(this.imageList, index, {
              //   file: item.path,
              //   status: 'fail',
              //   message: '上传失败',
              // })
            }
          })
          .catch((err) => {
            console.log(err, 'uploadHttp-catch')
            this.$set(this.imageList, index, {
              file: item.path,
              status: 'fail',
              message: '上传失败',
            })
          })
      })
    },
    deletePic(e) {
      console.log(e, 'deletePic')
      const index = this.imageList.findIndex((f) => f.url === e)
      this.imageList.splice(index, 1)
      this.$emit('input', this.imageList)
    },
    doImagePerview(i) {
      uni.previewImage({
        current: i,
        urls: this.imageList.map((x) => x.url),
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.common-upload {
  .image-list {
    display: flex;
    flex-wrap: wrap;
    .image-item {
      position: relative;
      width: 160rpx;
      height: 160rpx;
      background: #f4f5f7;
      margin: 0 20rpx 20rpx 0;
      overflow: hidden;
      .image {
        width: 100%;
        height: 100%;
      }
      .del-btn {
        position: absolute;
        top: 0;
        right: 0;
        background-color: #373737;
        height: 28rpx;
        width: 28rpx;
        display: flex;
        flex-direction: row;
        border-bottom-left-radius: 100px;
        align-items: center;
        justify-content: center;
        z-index: 3;
        .del-btn-icon {
          margin-top: -14rpx;
          margin-right: -6rpx;
          color: #fff;
          font-size: 24rpx;
        }
      }
    }
  }
}
</style>
