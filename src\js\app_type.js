import { getAuth } from '@/services/integral/business/business.js'
import { filePathDownload } from '@/services/index'
import store from '@/store/index.js'
import { getStaticImg } from '../utils/index.js'
import { getLifeAuth } from './utils.js'
export function openAppFunction(item, toType = 'navigateTo') {

  // 建设页面
  if (item.active == 0) {
    let title = item.text
      ? item.text
      : item.title
      ? item.title
      : item.name
      ? item.name
      : ''

    uni.navigateTo({
      url: '/pages/my/webView/underPage?title=' + title,
    })
    return
  }
  // isOnlyEnterprise
  // 企业办理
  if (item.isOnlyEnterprise == 1 && store.state.userInfo.userType == '01') {
    uni.showModal({
      content: '该服务仅限企业办理',
      confirmText: '确定',
      showCancel: false,
      success: function (res) {
        if (res.confirm) {
          console.log('用户点击确定')
        } else if (res.cancel) {
          console.log('用户点击取消')
        }
      },
    })
    return
  }

  console.log(store, 'store-store')
  console.log(item, 'item-item')
  if (item.text == '商户端') {
    hasAuth(item, toType)
    return
  }

  if (item.needLogin && !store.state.token) {
    uni.navigateTo({
      url: '/pages/my/login/entry',
    })
    return
  }
  // // 重要认证（需要进行第一步金融级实名认证才能进入，且必须经过二次认证）
  // if (item.isImportantAuth) {
  //   console.log(store.state.userInfo.idCard, 'store.state.userInfo.idCard')
  //   // getLifeAuth((e) => {
  //   //   console.log(e, 'eee-getLifeAuth')
  //   // })
  //   只有实人认证过的才能进入
  //   // if (!store.state.userInfo.idCard) {
  //   //   uni.showModal({
  //   //     title: '提示',
  //   //     content: '该应用需进行实人认证，是否立即前往实人认证？',
  //   //     cancelText: '取消',
  //   //     confirmText: '确定',
  //   //     success: (s) => {
  //   //       console.log(s, 'modal-确定')
  //   //       if (s.confirm) {
  //   //          // #ifdef H5
  //   //         uni[toType]({
  //   //           url: '/pages/my/login/auth/entry',
  //   //         })
  //   //         // #endif

  //   //         // #ifdef MP-WEIXIN
  //   //         uni.showToast({
  //   //           title:'微信端暂不支持此功能',
  //   //           icon:'none'
  //   //         })
  //   //         // #endif
  //   //       }
  //   //     },
  //   //   })
  //   //   return
  //   // }
  // }
  // 判断是否需要实名认证
  if (item.authType > 0) {
    const isRz = store.state.userInfo.sfrz
    if (isRz == '1') {
      if (item.authType === 2) {
        // 需要进行二次认证
      }
    } else {
      uni.showModal({
        title: '提示',
        content: '该应用需进行实人认证，是否立即前往实人认证？',
        cancelText: '取消',
        confirmText: '确定',
        success: (s) => {
          console.log(s, 'modal-确定')
          if (s.confirm) {
            // #ifdef H5
            uni[toType]({
              url: '/pages/my/login/auth/entry',
            })
            // #endif

            // #ifdef MP-WEIXIN
            uni.showToast({
              title: '微信端暂不支持此功能',
              icon: 'none',
            })
            // #endif
          }
        },
      })
    }
  }
  console.log(item, store.state.userInfo.sfrz, 'item-item')
  if (item.title === '实名认证') {
    if (store.state.userInfo.sfrz == '1') {
      uni.showToast({
        icon: 'none',
        title: '您已完成实名认证,无需再次认证',
      })
      return
    } else {
      uni[toType]({
        url: item.url,
      })
      return
    }
  }
  let title = item.text
    ? item.text
    : item.title
    ? item.title
    : item.name
    ? item.name
    : ''

  if (item.isMaintenance === 1 || item.is_maintenance) {
    uni[toType]({
      url: '/pages/affairList/under-maintenance/index?title=' + title,
    })
    return
  }
  // #ifdef MP-WEIXIN
  if (item.text == '医保诊疗项目') {
    openYbzlApp()
    return
  }
  // #endif

  if (
    (item.isAuth == 1 && toType !== 'redirectTo') ||
    (item.is_auth && toType !== 'redirectTo')
  ) {
    uni[toType]({
      url:
        '/pages/affairList/auth/auth?item=' +
        encodeURIComponent(JSON.stringify(item)),
    })
  } else {
    let typeOf = item.typesOf
      ? item.typesOf
      : item.types_of
      ? item.types_of
      : '自建服务'

    switch (typeOf) {
      case '三方服务':
        const extra = {
          url: item.url.trim(),
          title: item.text,
          type: 'iframe',
        }
        if (item.notitle === 1) {
          uni[toType]({
            url:
              '/pages/my/webView/webViewnotitle?extra=' +
              encodeURIComponent(JSON.stringify(extra)),
          })
        } else {
          uni[toType]({
            url:
              '/pages/my/webView/webView?extra=' +
              encodeURIComponent(JSON.stringify(extra)),
          })
        }
        break
      case '自建服务':
        let url = ''
        // #ifdef MP-WEIXIN
        url = item.url.trim()
        // #endif
        // #ifdef H5
        url =
          item.name === '15分钟生活圈'
            ? '/pages/lifeCircle/h5map/index'
            : item.url.trim()
        // #endif
        uni[toType]({
          url,
          fail: (err) => {
            console.log(err)
          },
        })
        break
      case '浙里办服务':
        uni.navigateToMiniProgram({
          appId: '',
          path: '',
          shortLink: item.url.trim(),
          success(res) {
            // 打开成功
          },
          fail(err) {
            if (!err.errMsg.includes('cancel')) {
              uni.showToast({
                title: err.errMsg.split(':')[1],
                icon: 'error',
              })
            }
            console.log(err)
          },
        })

        break
      case '三方小程序':
        uni.navigateToMiniProgram({
          appId: '',
          path: '',
          shortLink: item.url.trim(),
          success(res) {
            // 打开成功
          },
          fail(err) {
            if (!err.errMsg.includes('cancel')) {
              uni.showToast({
                title: err.errMsg.split(':')[1],
                icon: 'error',
              })
            }
            console.log(err)
          },
        })
        break
      default:
        uni.showToast({
          title: '应用配置错误',
          icon: 'error',
        })
        break
    }
  }
}

function openYbzlApp() {
  uni.downloadFile({
    url: getStaticImg('files/zjjbyl2023.pdf'), // 示例的url地址
    success: function (resinfo) {
      console.log('pdf协议文件已下载')
      const path = resinfo.tempFilePath
      console.log(path, resinfo)
      uni.openDocument({
        filePath: path,
        fileType: 'pdf',
        success: function (rest) {
          console.log('打开文件成功')
          console.log(rest)
        },
        fail: function (error) {
          console.log(error, '打开文件失败')
        },
      })
    },
    fail: function (err) {
      console.log('fail')
      console.log(err)
      uni.showToast({
        icon: 'none',
        title: '下载文件失败',
      })
    },
  })

  // filePathDownload(getStaticImg('files/zjjbyl2023.pdf')).then(res => {
  //   console.log(res, 'res-res')
  // }).catch((err) => {
  //   console.log(err, 'filePathDownload-err')
  // })
}

function hasAuth(item, toType) {
  getAuth().then((res) => {
    if (res.code == 200) {
      uni[toType]({
        url: item.url.trim(),
        fail: (err) => {
          console.log(err)
        },
      })
    }
    uni.showToast({
      icon: 'none',
      title: res.msg,
    })
  })
}
