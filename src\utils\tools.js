// keepWord是要传入的不脱敏的位数


export function hideIdCard(idCard, keepWord) {
    if (!idCard) {
      return idCard
    }

    let digits = keepWord ? keepWord : 2,
      reg = new RegExp(`(^\\w{${digits}})(\\w+)(\\w{${digits}}$)`, 'g')

    return idCard.replace(reg, function (...args) {
      let tempStr = ''

      if (args[2] && args[2].length) {
        for (let i = 0, len = args[2].length; i < len; i++) {
          tempStr += '*'
        }
      }

      return args[1] + tempStr + args[3]
    })
 }


export function hideName(name) {
   if (!name) {
     return name
   }
   return name.replace(/(^.{1})(.+)$/g, function (...args) {
     let tempStr = ''
     if (args[2] && args[2].length) {
       tempStr = Array.from({
         length: args[2].length
       }).join('*')
     }
     let res = name.length === 2 ? (  name.slice(0,1))+'*' : (args[1] + tempStr + name.slice(-1))
     return res
   })
 }

 export function   hidePhone(phone) {
     let reg = /(\d{3})\d*(\d{4})/
     return phone ? phone.replace(reg, '$1****$2') : ''
   }

  export function     hideBankAccount(bankAccount) {
       let reg = /(\d{3})(\d+)(\d{4})/
       var strLength = bankAccount.match(reg)[2].length;
       let strValue = ''
       for(let i = 0; i < strLength; i++) {
         strValue+='*'
       }
       return bankAccount.replace(reg,  "$1" + strValue + "$3")
     }
