import { http } from '@/utils/http'

// 获取公告列表
export const getNoticeList = (data) => {
  const params = {
    url: `/screen/yfk/ajhYfkPtgggl/list`,
    method: 'GET',
    data
  }
  return http(params)
}
// 获取轮播图列表
export const getImgList = () => {
  const params = {
    url: `/screen/yfk/ajhYfkLbtgl/list`,
    method: 'GET',
  }
  return http(params)
}
//获取商铺列表
export function getShopList(data) {
  return http({
    url: '/screen/yfk/ajhYfkSpgl/homePagelist',
    method: 'get',
    data
  })
}

// 下单接口
export const reqWxPay = (data) => {
  const params = {
   url: '/screen/pay/wxpay/reqWxPay',
   method: 'POST',
   data,
  }
  return http(params)
 }
//  订单支付接口
export const reqWxPayFk = (data) => {
  const params = {
   url: '/screen/pay/wxpay/reqWxPayFk',
   method: 'POST',
   data,
  }
  return http(params)
 }
