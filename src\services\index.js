import { http } from '@/utils/http'

export const login = (data) => {
  const params = {
    url: '/login',
    method: 'POST',
    data,
  }
  return http(params)
}

export const fetchMapTypeList = () => {
  const params = {
    url: '/life/mapPoiType/list',
  }
  return http(params)
}

// 通用接口查询
export const getIndexData = (data) => {
  const params = {
    url: '/screen/base/indexId',
    method: 'POST',
    data,
  }
  return http(params)
}

// 通用接口添加
export const postIndexData = (data) => {
  const params = {
    url: '/screen/base/save',
    method: 'POST',
    data,
  }
  return http(params)
}

// 通用接口删除
export const delIndexData = (data) => {
  const params = {
    url: `/screen/base/delete`,
    method: 'DELETE',
    data,
  }
  return http(params)
}
// /*/screen/base/update
// 通用接口修改
export const updataIndexData = (data) => {
  const params = {
    url: `/screen/base/update`,
    method: 'DELETE',
    data,
  }
  return http(params)
}

// 默认应用（拖动排序）
export function defaultAppOrder(data) {
  const params = {
    url: `/screen/application/defaultAppOrder`,
    method: 'POST',
    data,
  }
  return http(params)
}

// 八婺生活圈通用接口
export function getReqBusiness(data) {
  const params = {
    url: `/screen/reqBusiness`,
    method: 'POST',
    data,
  }
  return http(params)
}

// 下载文件
export function filePathDownload(filePath) {
  const params = {
    url: `/screen/file/filePathDownload?filePath=${filePath}`,
    method: 'GET',
  }
  return http(params)
}


// 浙商银行sdk调用返回h5url
export function getZssdkurl(data) {
  const params = {
    url: `/screen/zlbReceive/zssdkurl`,
    method: 'GET',
    data
  }
  return http(params)
}


// 用户出分和获取地址
export function getuserAuthorizationUrlLs(data) {
  const params = {
    url: `/screen/zlbReceive/getuserAuthorizationUrlLs`,
    method: 'POST',
    data
  }
  return http(params)
}

// 用户授权状态
export function getuserAuthorizationStatus(data) {
  const params = {
    url: `/screen/zlbReceive/getuserAuthorizationStatus`,
    method: 'POST',
    data
  }
  return http(params)
}