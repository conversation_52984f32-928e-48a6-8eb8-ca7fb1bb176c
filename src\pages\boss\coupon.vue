<template>
  <view style="min-height: 100vh; background: #eef1f4; padding-top: 1rpx">
    <view class="option-item">
      <view class="info">
        <text class="title">预付卡余额核销</text>
        <text class="label">核销优惠券时，使用预付卡余额支付</text>
      </view>
      <text class="next-btn" @tap="toHX(1)">去核销</text>
    </view>
    <view class="option-item">
      <view class="info">
        <text class="title">现金核销</text>
        <text class="label">仅核销优惠券，商家自行收款</text>
      </view>
      <text class="next-btn" @tap="toHX(2)">去核销</text>
    </view>
  </view>
</template>

<script>
import { shopHx } from '@/services/yfk/boss/hxjl.js'
export default {
  data() {
    return {
      code: '',
    }
  },
  onLoad(data) {
    this.code = JSON.parse(decodeURIComponent(data.codedata)).code
  },
  methods: {
    // 跳转至去核销页
    toHX(type) {
      if (type == 1) {
        uni.navigateTo({
          url:
            '/pages/boss/smhx?type=' +
            encodeURIComponent(JSON.stringify(type)) +
            '&code=' +
            encodeURIComponent(JSON.stringify(this.code)),
        })
      } else {
        uni.showLoading({ mask: true })
        shopHx({ type: 1, code: this.code })
          .then((res) => {
            uni.hideLoading()
            if (res.code === 200) {
              uni.redirectTo({
                url:
                  '/pages/boss/hxjg-xj?order=' +
                  encodeURIComponent(JSON.stringify(res.data)),
              })
            } else {
              uni.showToast({
                title: res.msg,
                position: 'bottom',
                duration: 3000,
              })
            }
          })
          .catch(() => {
            uni.hideLoading()
          })
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.option-item {
  margin: 30rpx 30rpx 0 30rpx;
  background: #ffffff;
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 20rpx;
  .info {
    .title {
      margin-bottom: 10rpx;
      display: block;
      font-family: 'Source Han Sans-Regular';
      color: #222222;
      font-size: 32rpx;
    }
    .label {
      display: block;
      font-family: 'Source Han Sans-Regular';
      font-weight: 400;
      font-size: 24rpx;
      color: #999999;
    }
  }
  .next-btn {
    width: 130rpx;
    line-height: 60rpx;
    background: rgba(255, 60, 0, 0.1);
    border-radius: 10rpx;
    color: #ff3c00;
    font-family: 'Source Han Sans-Regular';
    font-size: 28rpx;
    font-weight: 400;
    text-align: center;
  }
}
</style>
