// 管理账号信息
const USERS_KEY = 'USERS_KEY'
const STATE_KEY = 'STATE_KEY'
const FILE_SERVER = 'http://172.20.83.38:8080//ptyj/fileView/viewFile?fid='
// const DATABASE = 'http://192.168.137.1:8080';
// zconst WEBSOCKETPATH = 'ws://192.168.1.104:8999/h5websocket'
const WEBSOCKETPATH = 'ws://117.175.47.63:9003'
const ptid = 'PT0001'
// const ptid = 'PT0001'
// #ifdef APP-PLUS||MP-WEIXIN
const DATABASE = 'http://117.175.47.63:9003'
const DATABASEPATH = DATABASE + '/app-home'
const LOGINPATH = DATABASE + '/app-onelogin' //测试
const LDSTPATH = DATABASE + '/api'
const MAPPATH = DATABASE + '/api-map'
const VIDEOPATH = DATABASE + '/video-server'
// #endif

// #ifdef H5
const DATABASE = 'http://117.175.47.63:9003'
// const DATABASEPATH = '/app-home';
const DATABASEPATH = '/newapp-home'

// const LOGINPATH = '/app-onelogin'; //测试
const LOGINPATH = '/api'
const MAPPATH = '/api-map'
const VIDEOPATH = '/video-server'

// const LOGINPATH = '/app-login';//正式
// const LOCALPATH = '/app-collect'
// #endif

// #ifdef APP-PLUS
// 日志方法引入
import log from '@/js/log.js'
// #endif

import { getDate, getTime } from '@/js/utils.js'
import store from '@/store/index.js'
const requestUrl = function (path, method, params, header) {
  let showLoading = true
  // setTimeout(() => {
  // 	if (showLoading)
  // 		uni.showLoading({
  // 			mask: true,
  // 			title: '加载中'
  // 		})
  // }, 1500)
  return new Promise((resolve, reject) => {
    if (header) {
      if (!header.token) {
        header.token = store.state.token
      }
    }
    let requestParams = {
      url: path,
      method: method,
      sslVerify: false,
      timeout: 30000,
      data: params,
      header: header,
    }
    // #ifdef APP-PLUS
    // log.writeLog(requestParams, false)
    // #endif

    uni.request({
      url: path,
      method: method,
      sslVerify: false,
      timeout: 30000,
      data: params,
      header: header,
      success: (res) => {
        // log.writeLog(res, false)
        res.requestParams = requestParams
        console.log('request success', res)
        if (res.statusCode == 200) {
          //token超时
          // console.log(header)
          if (header)
            if (
              (res.data.responsecode + '' == '10006' && !res.data.successful) ||
              (header.Authorization && res.data.code == 401)
            ) {
              uni.showToast({
                icon: 'none',
                title: '登录失效，请重新登录',
              })
              let msgParam = {
                method: 'logout',
                userId: store.state.userId,
              }
              if (store.state.cid) {
                getApp().sendMessage(msgParam, () => {
                  getApp().disconnectSocket()
                  setTimeout(() => {
                    store.commit('logout')
                    uni.redirectTo({
                      url: '/pages/my/login/start',
                    })
                  }, 2000)
                })
              } else {
                setTimeout(() => {
                  store.commit('logout')
                  uni.redirectTo({
                    url: '/pages/my/login/start',
                  })
                }, 2000)
              }
              return
            }
          //正常
          resolve(res)
          return
        }
        if (res.statusCode == 405) {
          uni.showToast({
            icon: 'none',
            title: '请求方法错误',
          })
        }
        reject(res)
      },
      fail: (err) => {
        // #ifdef APP-PLUS
        // log.writeLog(err, false)
        // #endif
        err.requestParams = requestParams
        console.log(':request fail', err)
        uni.showToast({
          icon: 'none',
          title: err.errMsg,
        })
        reject(err)
      },
      complete: (e) => {
        showLoading = false
        uni.hideLoading()
      },
    })
  })
}
export default {
  FILE_SERVER,
  DATABASE,
  DATABASEPATH,
  LOGINPATH,
  WEBSOCKETPATH,
  requestUrl,
  ptid,
  // LDSTPATH,
  MAPPATH,
  VIDEOPATH,

  // LOCALPATH
}
