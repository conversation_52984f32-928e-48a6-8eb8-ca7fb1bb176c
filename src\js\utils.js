import store from '@/store/index.js'
export function getSubStr(temp, len) {
  if (temp) {
    return temp.length > parseInt(len)
      ? temp.substr(0, parseInt(len)) + '...'
      : temp
  } else {
    return '暂无信息'
  }
}

export function getDate() {
  var date = new Date()
  var seperator1 = '-'
  var year = date.getFullYear()
  var month = date.getMonth() + 1
  var strDate = date.getDate()
  if (month >= 1 && month <= 9) {
    month = '0' + month
  }
  if (strDate >= 0 && strDate <= 9) {
    strDate = '0' + strDate
  }
  var currentdate = year + seperator1 + month + seperator1 + strDate
  return currentdate
}

export function getNewDate(dateTemp, days) {
  // var dateTemp = dateTemp.split("-");
  // var nDate = new Date(dateTemp[1] + '-' + dateTemp[2] + '-' + dateTemp[0]); //转换为MM-DD-YYYY格式
  var nDate = null
  if (dateTemp) nDate = new Date(dateTemp.replace(/-/g, '/'))
  else nDate = new Date()
  var millSeconds = Math.abs(nDate) + days * 24 * 60 * 60 * 1000
  var rDate = new Date(millSeconds)
  var year = rDate.getFullYear()
  var month = rDate.getMonth() + 1
  if (month < 10) month = '0' + month
  var date = rDate.getDate()
  if (date < 10) date = '0' + date
  return year + '-' + month + '-' + date
}

export function getLeftTime(d1) {
  //di作为一个变量传进来
  //如果时间格式是正确的，那下面这一步转化时间格式就可以不用了
  let dateBegin = new Date(d1.replace(/-/g, '/')) //将-转化为/，使用new Date
  let dateEnd = new Date() //获取当前时间
  let dateDiff = Math.abs(dateEnd.getTime() - dateBegin.getTime()) //时间差的毫秒数
  let dayDiff = Math.floor(dateDiff / (24 * 3600 * 1000)) //计算出相差天数
  let leave1 = dateDiff % (24 * 3600 * 1000) //计算天数后剩余的毫秒数
  let hours = Math.floor(leave1 / (3600 * 1000)) //计算出小时数
  //计算相差分钟数
  let leave2 = leave1 % (3600 * 1000) //计算小时数后剩余的毫秒数
  let minutes = Math.floor(leave2 / (60 * 1000)) //计算相差分钟数
  //计算相差秒数
  let leave3 = leave2 % (60 * 1000) //计算分钟数后剩余的毫秒数
  let seconds = Math.round(leave3 / 1000)
  return dayDiff + '天' + hours + '小时' + minutes + '分钟'
  console.log(
    ' 相差 ' +
      dayDiff +
      '天 ' +
      hours +
      '小时 ' +
      minutes +
      ' 分钟' +
      seconds +
      ' 秒'
  )
  console.log(
    dateDiff + '时间差的毫秒数',
    dayDiff + '计算出相差天数',
    leave1 + '计算天数后剩余的毫秒数',
    hours + '计算出小时数',
    minutes + '计算相差分钟数',
    seconds + '计算相差秒数'
  )
}

export function getTime() {
  let date = new Date()
  let hour = date.getHours() < 10 ? '0' + date.getHours() : date.getHours()
  let minute =
    date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()
  let second =
    date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()
  return hour + ':' + minute + ':' + second
}

export function getDatetime(dateNum) {
  if (!dateNum) {
    return '暂无信息'
  }
  let date = new Date(dateNum)
  let year = date.getFullYear() + '-'
  let month =
    (date.getMonth() + 1 < 10
      ? '0' + (date.getMonth() + 1)
      : date.getMonth() + 1) + '-'
  let day = date.getDate() + ' '
  return year + month + day
}

export function formatStr(str) {
  if (str) {
    return str
  } else if (str === null) {
    return '无'
  } else {
    return '无'
  }
}
//转换时间、数值
export function formatDateTime(input, type) {
  if (!type) type = 'S'
  if (!input || ~['undefined', '无'].indexOf(input)) return '无'
  if (type === 'D') {
    let date = new Date(input)
    let y = date.getFullYear()
    let m = date.getMonth() + 1
    m = m < 10 ? '0' + m : m
    let d = date.getDate()
    d = d < 10 ? '0' + d : d
    return y + '-' + m + '-' + d
  } else if (type == 'N') {
    return new Number(input)
  } else {
    return input
  }
}
export function backAndRefresh(bool) {
  //是否刷新上级页面
  let refresh = bool ? bool : false
  let pages = getCurrentPages() // 当前页面
  let beforePage = pages[pages.length - 2] // 前一个页面
  setTimeout(() => {
    uni.navigateBack({
      success: function () {
        if (refresh) {
          console.log('刷新上级页面')
          beforePage.onLoad() // 执行前一个页面的onLoad方法
        }
      },
    })
  }, 1500)
}
// export function getUUID() {
//     var d = new Date().getTime();
//     if (window.performance && typeof window.performance.now === "function") {
//         d += performance.now(); //use high-precision timer if available
//     }
//     var uuid = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
//         var r = (d + Math.random() * 16) % 16 | 0;
//         d = Math.floor(d / 16);
//         return (c == 'x' ? r : (r & 0x3 | 0x8)).toString(16);
//     });
//     return uuid;
// }

export function getUUID() {
  var s = []
  var hexDigits = '0123456789abcdef'
  for (var i = 0; i < 36; i++) {
    s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1)
  }
  s[14] = '4' // bits 12-15 of the time_hi_and_version field to 0010
  s[19] = hexDigits.substr((s[19] & 0x3) | 0x8, 1) // bits 6-7 of the clock_seq_hi_and_reserved to 01
  s[8] = s[13] = s[18] = s[23] = '-'

  var uuid = s.join('')
  return uuid
}

function getQueryParams(str) {
  var search = str
  try {
    let jsonParam = Object.fromEntries(new URLSearchParams(str))
    // console.log(jsonParam)
    return jsonParam
    // return JSON.parse('{"' + search.replace(/"/g, '\\"').replace(/&(?=\w+=)/g, '","').replace(/(?<!=\w+)=/g, '":"') + '"}')
  } catch (e) {
    return {}
  }
}
//正则解析get参数
export function getURLParameters(uri) {
  var idx = uri.indexOf('?')
  if (idx != -1) {
    var paramUrl = uri.substr(idx + 1)
    return getQueryParams(paramUrl)
  } else {
    return getQueryParams(uri)
  }
}

//登录用的加密
export function encode64(val) {
  // base64加密开始
  var keyStr =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/='
  var output = ''
  var chr1,
    chr2,
    chr3 = ''
  var enc1,
    enc2,
    enc3,
    enc4 = ''
  var i = 0
  do {
    chr1 = val.charCodeAt(i++)
    chr2 = val.charCodeAt(i++)
    chr3 = val.charCodeAt(i++)
    enc1 = chr1 >> 2
    enc2 = ((chr1 & 3) << 4) | (chr2 >> 4)
    enc3 = ((chr2 & 15) << 2) | (chr3 >> 6)
    enc4 = chr3 & 63
    if (isNaN(chr2)) {
      enc3 = enc4 = 64
    } else if (isNaN(chr3)) {
      enc4 = 64
    }
    output =
      output +
      keyStr.charAt(enc1) +
      keyStr.charAt(enc2) +
      keyStr.charAt(enc3) +
      keyStr.charAt(enc4)
    chr1 = chr2 = chr3 = ''
    enc1 = enc2 = enc3 = enc4 = ''
  } while (i < val.length)
  return output
}

//判断文件后缀名
export function fileType(fileName) {
  var fileExtension = fileName.substring(fileName.lastIndexOf('.') + 1)
  var suffix = fileExtension || ''
  var typeList = ['image', 'audio', 'video', 'file']
  var length = typeList.length - 1
  var suffixJson = {
    image: ['png', 'jpg', 'jpeg', 'gif', 'ico', 'bmp', 'pic', 'tif'],
    audio: ['mp3', 'ogg', 'wav', 'acc', 'vorbis', 'silk'],
    video: ['mp4', 'webm', 'avi', 'rmvb', '3gp', 'flv'],
  }
  var resultList = []

  for (var attr in suffixJson) {
    resultList.push(!!~suffixJson[attr].indexOf(suffix))
  }

  var posIndex = resultList.indexOf(true)

  return posIndex != -1 ? typeList[posIndex] : typeList[length]
}

// 获取经纬度
export function getPosition(callback) {
  const watchPosition = (event) => {
    if (event.data.cmd == 'getPosition') {
      console.log(event.data, 'event.data-watchmsg')
      if (event.data.value && event.data.value.longitude) {
        let res = event.data.value
        callback(res)
        setTimeout(() => {
          window.removeEventListener('message', watchPosition)
        }, 2000)
      } else {
        uni.hideLoading()
      }
    }
  }
  // #ifdef MP-WEIXIN
  uni.getLocation({
    type: 'gcj02',
    geocode: true,
    success: function (res1) {
      console.log('当前位置的经度：' + res1.longitude)
      console.log('当前位置的纬度：' + res1.latitude)
      callback(res1)
    },
    fail: function (err) {
      console.log(err, 'getLocation-fail')
      uni.hideLoading()
      callback({ longitude: '119.644984', latitude: '29.074466' })
    },
    complete: function (e) {
      console.log(e)
    },
  })
  // #endif
  // #ifdef H5
  // 线上h5（app）需要和壳交互
  window.parent.postMessage(
    {
      cmd: 'getPosition',
      options: {
        type: 'gcj02',
      },
    },
    '*'
  )
  window.addEventListener('message', watchPosition)
  // #endif
}

// 阿里云APP二次活体认证(无需传姓名和身份证号)
export function getLifeAuth(callback) {
  const watchAuth = (event) => {
    if (event.data.cmd == 'getLifeAuth') {
      console.log(event.data, 'event.data-getLifeAuth')
      callback(event.data)
    }
  }
  // #ifdef H5
  // 线上h5（app）需要和壳交互
  window.parent.postMessage(
    {
      cmd: 'startLifeAuth',
      options: {
        name: store.state.userInfo.name,
        idCard: store.state.userInfo.idCard,
        token: store.state.token,
      },
    },
    '*'
  )
  window.addEventListener('message', watchAuth)
  // #endif
}

// 检查app的权限
export function checkAppAuth(id, callback) {
  console.log(id, 'id-checkAppAuth')
  window.parent.postMessage(
    {
      cmd: 'getAppAuth',
      options: {
        id,
      },
    },
    '*'
  )
  window.addEventListener('message', watchAppAuth)
  const watchAppAuth = (event) => {
    if (event.data.cmd == 'getAppAuth') {
      console.log(event.data, 'event.data-getAppAuth')
      // if (event.data.value && event.data.value.longitude) {
      //   let res = event.data.value
      //   callback(res)
      //   setTimeout(() => {
      //     window.removeEventListener('message', watchPosition)
      //   }, 2000)
      // } else {
      //   uni.hideLoading()
      // }
    }
  }
}
