## 1.1.0（2022-03-15）
新增禁用滑动 disable 属性
## 1.0.9（2022-03-14）
新增get-end-detail事件，获取滑动结束时的top信息对象
## 1.0.8（2022-03-11）
更新了setBottom手动设置上拉框高度方法，通过this.$refs.上拉框.setBottom调用
## 1.0.7（2022-03-09）
对nvue做了些兼容处理，但存在transition失效问题，使用nvue时建议关闭自动复位
## 1.0.6（2022-03-04）
-
## 1.0.5（2022-03-04）
更新了适配自定义navbar、tabbar的解决方案，通过customSafeArea属性设置自定义的安全高度
## 1.0.4（2022-03-03）
因为组件是从项目中抽离出来的，而项目中使用的是自定义navbar，以至于这插件有极大的问题，这次更新将组件的基准改为了uniapp原生navbar，之后的更新会兼容自定义navbar的情况
## 1.0.3（2022-03-03）
-
## 1.0.2（2022-03-03）
删掉日志打印
## 1.0.1（2022-03-03）
修复了首次上传因粗心大意导致的bug
## 1.0.0（2022-03-03）
首次上传，只有最基本的功能
