<script>
export default {
  onLaunch: function (options) {
    console.log('app  onLaunch')
    uni.removeStorageSync('location')
    this.addRouterListen()
    const d = uni.getSystemInfoSync()
    uni.setStorageSync('systemInfo', d)
    const scene = options.scene
    uni.setStorageSync('scene', scene)
  },
  onShow: function () {
    uni.hideTabBar()
    // process.env.NODE_ENV !== 'development'&&
    if (this.$store.state.token) {
      uni.reLaunch({
        url: '/pages/boss/index',
      })
    }
  },
  onHide: function () {},
  globalData: {
    switchId: '',
  },
  watch: {},
  methods: {
    addRouterListen() {
      // #ifdef MP-WEIXIN
      uni.addInterceptor('navigateTo', {
        invoke(args) {
          const pages = getCurrentPages() // 获取加载的页面
          const newPages = pages.map((item) => item.route)

          const nowUrl = args.url
          const lastPage = newPages.length && newPages[newPages.length - 1]
          if (
            nowUrl.includes('/login/wxLogin') &&
            lastPage.includes('/login/wxLogin')
          ) {
            console.log('拦截多次跳转login')
            return false
          }
          return true
        },
        success(args) {
          console.log('app--拦截器success', args)
        },
        fail(err) {
          console.log('interceptor-fail', err)
        },
        complete(res) {
          console.log('interceptor-complete', res)
        },
      })
      uni.addInterceptor('redirectTo', {
        invoke(args) {
          const pages = getCurrentPages() // 获取加载的页面
          const newPages = pages.map((item) => item.route)

          const nowUrl = args.url
          const lastPage = newPages.length && newPages[newPages.length - 1]

          if (
            nowUrl.includes('/login/wxLogin') &&
            lastPage.includes('/login/wxLogin')
          ) {
            console.log('拦截多次跳转login')
            return false
          }
          return true
        },
        success(args) {
          console.log('app--拦截器success', args)
        },
        fail(err) {
          console.log('interceptor-fail', err)
        },
        complete(res) {
          console.log('interceptor-complete', res)
        },
      })
      // #endif
    },
  },
}
</script>

<style lang="scss">
@import 'uview-ui/index.scss';
@import './static/iconfont/iconfont.css';
@import './static/font.css';

/*每个页面公共css */
// @import '@/uni_modules/uview-ui/index.scss';
body {
  // font-family: 'SHSC-R';
  color: #222;
  // padding-top: 57rpx;
}

/* 原生组件模式下需要注意组件外部样式 */
custom-component {
  width: 100%;
  min-height: 100%;
  display: flex;
}

/* 原生组件模式下需要注意组件外部样式 */
m-input {
  width: 100%;
  min-height: 100%;
  display: flex;
}

.pad-iframe {
  width: 100%;
  height: 100%;
  border: unset;
}

// 其中var(--status-bar-height)为系统栏的高度
// margin-top: var(--status-bar-height);

/* #ifdef H5 */
uni-page-head .uni-page-head {
  height: calc(var(--safe-area-top) + 180rpx) !important;
  padding-top: 88rpx;
  // padding-top: var(--status-bar-height);
}

uni-page-head[uni-page-head-type='default'] ~ uni-page-wrapper {
  margin-top: calc(78rpx - env(safe-area-inset-top)) !important;
  // margin-top: -60rpx;
  // margin-top: env(safe-area-inset-top) !important;
  // margin-top: var(--status-bar-height);
}

uni-page-head[uni-page-head-type='default'] ~ uni-page-wrapper {
  height: calc(100% - 170rpx - env(safe-area-inset-top)) !important;
}

/* #endif */

.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}

::v-deep .uni-tabbar__icon {
  // width: 26px !important;
  // height: 20px !important;
}
</style>
