<template>
  <view>
    <view class="options-container">
      <text class="title">核销金额</text>
      <view class="nums-container">
        <text>￥</text>
        <input type="text" placeholder="请输入" v-model="value" disabled />
      </view>
      <button class="confirm-btn" @tap="confirmHX">确认核销</button>
    </view>
    <u-keyboard
      mode="number"
      @change="valChange"
      @backspace="backspace"
      :show="true"
      :overlay="false"
      :tooltip="false"
    ></u-keyboard>
    <u-notify ref="uNotify" message="Hi uView"></u-notify>
  </view>
</template>

<script>
import { sendXHJE } from '@/services/yfk/myOrder'
import { shopHx } from '@/services/yfk/boss/hxjl.js'
export default {
  data() {
    return {
      value: '',
      code: '',
      message: true,
      type: null,
    }
  },
  methods: {
    // 按键被点击(点击退格键不会触发此事件)
    valChange(val) {
      // 将每次按键的值拼接到value变量中，注意+=写法
      this.value += val
    },
    // 退格键被点击
    backspace() {
      // 删除value的最后一个字符
      if (this.value.length)
        this.value = this.value.substr(0, this.value.length - 1)
    },
    // 正则判断金额是否有效
    isValidAmount(nums) {
      let regex = /^\d+(\.\d{1,})?$/
      return regex.test(nums)
    },
    // 确认核销
    confirmHX() {
      if (this.isValidAmount(this.value)) {
        // if (this.type) {
        //   uni.showLoading({ mask: true })
        //   shopHx({ type: 2, code: this.code, amount: Number(this.value) })
        //     .then((res) => {
        //       uni.hideLoading()
        //       if (res.code === 200) {
        //         uni.redirectTo({
        //           url:
        //             '/pages/boss/hxjg-xj?order=' +
        //             encodeURIComponent(JSON.stringify(res.data)),
        //         })
        //       } else {
        //         uni.showToast({
        //           title: res.msg,
        //           position: 'bottom',
        //           duration: 3000,
        //           icon: 'none',
        //         })
        //       }
        //     })
        //     .catch(() => {
        //       uni.showToast({
        //         title: res.msg,
        //         position: 'bottom',
        //         duration: 3000,
        //         icon: 'none',
        //       })
        //       uni.hideLoading()
        //     })
        // } else {
        uni.showLoading({ mask: true })
        shopHx({
          amount: Number(this.value),
          code: this.code,
          hxType: 1,
          // code:'eyJkYXRlVGltZSI6MTcyNTU4ODE2MjMwMSwicGFyYW1zIjp7fSwidHlwZSI6MSwidXNlcklkIjoxMDAwNjF9'
        })
          .then((res) => {
            uni.hideLoading()
            if (res.code == 200) {
              uni.redirectTo({
                url:
                  '/pages/boss/hxjg?orderId=' +
                  encodeURIComponent(JSON.stringify(res.data)),
              })
            } else {
              uni.showToast({
                title: res.msg,
                position: 'bottom',
                duration: 3000,
              })
            }
          })
          .catch(() => {
            uni.showToast({
              title: res.msg,
              position: 'bottom',
              duration: 3000,
            })
            uni.hideLoading()
          })
        // }
      } else {
        this.$refs.uNotify.show({
          type: 'error',
          message: '请输入有效金额！',
          duration: 1000 * 3,
          fontSize: 16,
          safeAreaInsetTop: true,
        })
      }
    },
  },
  onLoad(data) {
    // if (data.type) {
    //   this.type = JSON.parse(decodeURIComponent(data.type))
    //   this.code = JSON.parse(decodeURIComponent(data.code))
    // } else {
    this.code = JSON.parse(decodeURIComponent(data.codedata)).code
    // }
  },
}
</script>

<style lang="scss" scoped>
page {
  background: white;
}
.options-container {
  padding: 60rpx;
  font-family: 'Source Han Sans-Bold';
  font-weight: 700;
  color: #191919;
  .title {
    display: block;
    font-size: 48rpx;
    text-align: center;
  }
  .nums-container {
    margin: 100rpx 0 120rpx;
    padding: 40rpx 0;
    border-bottom: 1rpx solid #d7d8dc;
    text {
      margin-right: 20rpx;
      display: inline-block;
      font-size: 36rpx;
      vertical-align: middle;
    }
    input {
      display: inline-block;
      vertical-align: middle;
      font-weight: 400;
      font-size: 36rpx;
      font-family: 'Source Han Sans-Regular';
    }
  }
  .confirm-btn {
    width: 300rpx;
    line-height: 90rpx;
    background: #ff3c00;
    font-family: 'Source Han Sans-Medium';
    font-weight: 500;
    font-size: 32rpx;
    color: #ffffff;
    border-radius: 100rpx;
  }
  .confirm-btn::after {
    border: 0;
  }
}
</style>
