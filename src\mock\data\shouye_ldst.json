{"shouye_fav": [{"menus": [{"icon": "iconrenwu1", "text": "浙江健康码", "subText": "", "color": "#2b35b2", "url": "/pages/my/personalDataBank/healthCode", "bgUri": "/szzy/h5/static/img/shouyeBc/<EMAIL>", "imgUri": "/static/img/shouye_icon/qr.png", "extra": {"title": "浙江健康码"}}, {"icon": "iconrenwu1", "text": "一码通", "subText": "", "color": "#2b35b2", "url": "/pages/codePage/codePage", "bgUri": "/szzy/h5/static/img/shouyeBc/<EMAIL>", "imgUri": "/static/img/my/personalDataBank/健康码.png", "extra": {"title": "一码通", "type": "iframe", "url": "https://uniapp.dcloud.net.cn/collocation/pages.html#tabbar"}}, {"icon": "iconrenwu1", "text": "个人数据宝", "subText": "", "color": "#2b35b2", "url": "/pages/my/personalDataBank/index", "bgUri": "/szzy/h5/static/img/shouyeBc/<EMAIL>", "imgUri": "/static/img/my/personalDataBank/个人数据宝.png", "extra": {"title": "个人数据宝", "type": "iframe", "url": "http://192.168.110.174:8603/m/service/water"}}, {"icon": "iconrenwu1", "text": "个人证照", "subText": "", "color": "#2b35b2", "url": "/pages/personal-information/certificate/index", "bgUri": "/szzy/h5/static/img/shouyeBc/<EMAIL>", "imgUri": "/static/img/personal/个人证照.png", "extra": {"title": "个人证照", "type": "iframe", "url": "http://192.168.110.174:8603/m/service/water"}}]}], "shouye_app": [{"menus": [{"icon": "iconrenwu1", "text": "应用名称", "subText": "", "color": "#2b35b2", "url": "/pages/under-develop/under-develop", "bgUri": "/szzy/h5/static/img/shouyeBc/<EMAIL>", "imgUri": "/static/img/shouye_icon/icon1.png", "extra": {"title": "应用名称"}}, {"icon": "iconrenwu1", "text": "应用名称", "subText": "", "color": "#2b35b2", "url": "/pages/under-develop/under-develop", "bgUri": "/szzy/h5/static/img/shouyeBc/<EMAIL>", "imgUri": "/static/img/shouye_icon/icon2.png", "extra": {"title": "应用名称"}}, {"icon": "iconrenwu1", "text": "应用名称", "subText": "", "color": "#2b35b2", "url": "/pages/under-develop/under-develop", "bgUri": "/szzy/h5/static/img/shouyeBc/<EMAIL>", "imgUri": "/static/img/shouye_icon/icon3.png", "extra": {"title": "应用名称"}}, {"icon": "iconrenwu1", "text": "一码通", "subText": "", "color": "#2b35b2", "url": "/pages/under-develop/under-develop", "bgUri": "/szzy/h5/static/img/shouyeBc/<EMAIL>", "imgUri": "/static/img/shouye_icon/icon4.png", "extra": {"title": "一码通", "type": "iframe", "url": "https://uniapp.dcloud.net.cn/collocation/pages.html#tabbar"}}, {"icon": "iconrenwu1", "text": "交通出行", "subText": "", "color": "#2b35b2", "url": "/pages/under-develop/under-develop", "bgUri": "/szzy/h5/static/img/shouyeBc/<EMAIL>", "imgUri": "/static/img/shouye_icon/icon1.png", "extra": {"title": "交通出行", "type": "iframe", "url": "http://192.168.110.174:8603/m/service/water"}}, {"icon": "iconrenwu1", "text": "应用名称", "subText": "", "color": "#2b35b2", "url": "/pages/under-develop/under-develop", "bgUri": "/szzy/h5/static/img/shouyeBc/<EMAIL>", "imgUri": "/static/img/shouye_icon/icon2.png", "extra": {"title": "应用名称", "type": "iframe", "url": "http://192.168.110.174:8603/m/service/water"}}, {"icon": "iconrenwu1", "text": "应用名称", "subText": "", "color": "#2b35b2", "url": "/pages/under-develop/under-develop", "bgUri": "/szzy/h5/static/img/shouyeBc/<EMAIL>", "imgUri": "/static/img/shouye_icon/icon3.png", "extra": {"title": "应用名称", "type": "iframe", "url": "http://192.168.110.174:8603/m/service/water"}}, {"icon": "iconrenwu1", "text": "应用名称", "subText": "", "color": "#2b35b2", "url": "/pages/under-develop/under-develop", "bgUri": "/szzy/h5/static/img/shouyeBc/<EMAIL>", "imgUri": "/static/img/shouye_icon/icon4.png", "extra": {"title": "应用名称", "type": "iframe", "url": "http://192.168.110.174:8603/m/service/water"}}, {"icon": "iconrenwu1", "text": "应用名称", "subText": "", "color": "#2b35b2", "url": "/pages/under-develop/under-develop", "bgUri": "/szzy/h5/static/img/shouyeBc/<EMAIL>", "imgUri": "/static/img/shouye_icon/icon1.png", "extra": {"title": "应用名称", "type": "iframe", "url": "http://192.168.110.174:8603/m/service/water"}}, {"icon": "iconrenwu1", "text": "应用名称", "subText": "", "color": "#2b35b2", "url": "/pages/under-develop/under-develop", "bgUri": "/szzy/h5/static/img/shouyeBc/<EMAIL>", "imgUri": "/static/img/shouye_icon/icon2.png", "extra": {"title": "应用名称", "type": "iframe", "url": "http://192.168.110.174:8603/m/service/water"}}, {"icon": "iconrenwu1", "text": "应用名称", "subText": "", "color": "#2b35b2", "url": "/pages/under-develop/under-develop", "bgUri": "/szzy/h5/static/img/shouyeBc/<EMAIL>", "imgUri": "/static/img/shouye_icon/icon3.png", "extra": {"title": "应用名称", "type": "iframe", "url": "http://192.168.110.174:8603/m/service/water"}}]}], "shouye_menu": [{"remote_id": 296, "title": "猜你喜欢", "limit": 4, "column": 4, "showMore": false, "size": "small", "bgUrl": "/szzy/h5/static/img/index/dj_src.png", "menus": [{"remote_id": 299, "icon": "iconrenwu1", "text": "停车缴费", "subText": "", "color": "#2b35b2", "url": "/pages/under-develop/under-develop", "imgUri": "/static/img/shouye_icon/icon1.png", "bgUri": "/h5/static/img/index/<EMAIL>", "extra": {"title": "停车缴费"}}, {"remote_id": 300, "icon": "iconrenwu1", "text": "实时路况", "subText": "", "color": "#2b35b2", "url": "/pages/under-develop/under-develop", "bgUri": "/h5/static/img/index/<EMAIL>", "imgUri": "/static/img/shouye_icon/icon2.png", "extra": {"title": "实时路况", "type": "iframe", "url": "http://192.168.110.174:8603/m/economy/epidemic"}}, {"remote_id": 301, "icon": "iconrenwu1", "text": "查停车场", "subText": "", "color": "#2b35b2", "url": "/pages/under-develop/under-develop", "bgUri": "/h5/static/img/index/<EMAIL>", "imgUri": "/static/img/shouye_icon/icon3.png", "extra": {"title": "查停车场", "type": "iframe", "url": "http://192.168.110.174:8603/m/economy/industry"}}, {"remote_id": 301, "icon": "iconrenwu1", "text": "查停车场", "subText": "", "color": "#2b35b2", "url": "/pages/under-develop/under-develop", "bgUri": "/h5/static/img/index/<EMAIL>", "imgUri": "/static/img/shouye_icon/icon4.png", "extra": {"title": "查停车场", "type": "iframe", "url": "http://192.168.110.174:8603/m/economy/industry"}}]}, {"remote_id": 296, "title": "特色服务", "limit": 4, "type": "scroll", "bgUrl": "/szzy/h5/static/img/index/dj_src.png", "menus": [{"remote_id": 299, "icon": "iconrenwu1", "text": "一件事服务", "subText": "", "color": "#2b35b2", "url": "/pages/under-develop/under-develop", "imgUri": "/static/img/index/ddb_img.png", "bgUri": "/h5/static/img/shouye_icon/speacial.png", "extra": {"title": "一件事服务"}}, {"remote_id": 300, "icon": "iconrenwu1", "text": "秒办秒批服务", "subText": "", "color": "#2b35b2", "url": "/pages/under-develop/under-develop", "bgUri": "/h5/static/img/shouye_icon/speacial.png", "imgUri": "/static/img/index/zz_img.png", "extra": {"title": "秒办秒批服务", "type": "iframe", "url": "http://192.168.110.174:8603/m/economy/epidemic"}}, {"remote_id": 301, "icon": "iconrenwu1", "text": "十分钟生活圈", "subText": "", "color": "#2b35b2", "url": "/pages/lifeCircle/index", "bgUri": "/h5/static/img/shouye_icon/speacial.png", "imgUri": "/static/img/index/jijian_img.png", "extra": null}, {"remote_id": 301, "icon": "iconrenwu1", "text": "查停车场", "subText": "", "color": "#2b35b2", "url": "/pages/under-develop/under-develop", "bgUri": "/h5/static/img/shouye_icon/speacial.png", "imgUri": "/static/img/index/jijian_img.png", "extra": {"title": "查停车场", "type": "iframe", "url": "http://192.168.110.174:8603/m/economy/industry"}}]}, {"remote_id": 296, "title": "交通出行", "limit": 3, "column": 1, "bgUrl": "/szzy/h5/static/img/index/dj_src.png", "menus": [{"remote_id": 299, "icon": "iconrenwu1", "text": "停车缴费", "subText": "高效快速", "color": "#2b35b2", "url": "/pages/under-develop/under-develop", "imgUri": "/static/img/shouye_icon/icon4.png", "bgUri": "/h5/static/img/index/<EMAIL>", "extra": {"title": "停车缴费"}}, {"remote_id": 300, "icon": "iconrenwu1", "text": "实时路况", "subText": "路线查询", "color": "#2b35b2", "url": "/pages/under-develop/under-develop", "bgUri": "/h5/static/img/index/<EMAIL>", "imgUri": "/static/img/shouye_icon/icon1.png", "extra": {"title": "实时路况", "type": "iframe", "url": "http://192.168.110.174:8603/m/economy/epidemic"}}, {"remote_id": 301, "icon": "iconrenwu1", "text": "查停车场", "subText": "停车位检测", "color": "#2b35b2", "url": "/pages/under-develop/under-develop", "bgUri": "/h5/static/img/index/<EMAIL>", "imgUri": "/static/img/index/jijian_img.png", "extra": {"title": "查停车场", "type": "iframe", "url": "http://192.168.110.174:8603/m/economy/industry"}}]}, {"remote_id": 283, "title": "医疗保障", "limit": 4, "column": 1, "bgUrl": "/szzy/h5/static/img/index/jj_src.png", "menus": [{"remote_id": 299, "icon": "iconrenwu1", "text": "疫苗预约", "subText": "预约登记", "color": "#2b35b2", "url": "/pages/under-develop/under-develop", "imgUri": "/static/img/shouye_icon/icon1.png", "bgUri": "/h5/static/img/index/<EMAIL>", "extra": {"title": "疫苗预约"}}, {"remote_id": 300, "icon": "iconrenwu1", "text": "预约挂号", "subText": "三家医院", "color": "#2b35b2", "url": "/pages/under-develop/under-develop", "bgUri": "/h5/static/img/index/<EMAIL>", "imgUri": "/static/img/shouye_icon/icon2.png", "extra": {"title": "预约挂号", "type": "iframe", "url": "http://192.168.110.174:8603/m/economy/epidemic"}}, {"remote_id": 301, "icon": "iconrenwu1", "text": "体检服务", "subText": "申请入口", "color": "#2b35b2", "url": "/pages/under-develop/under-develop", "bgUri": "/h5/static/img/index/<EMAIL>", "imgUri": "/static/img/shouye_icon/icon3.png", "extra": {"title": "体检服务", "type": "iframe", "url": "http://192.168.110.174:8603/m/economy/industry"}}, {"remote_id": 301, "icon": "iconrenwu1", "text": "查停车场", "subText": "查看详情", "color": "#2b35b2", "url": "/pages/under-develop/under-develop", "bgUri": "/h5/static/img/index/<EMAIL>", "imgUri": "/static/img/shouye_icon/icon4.png", "extra": {"title": "查停车场", "type": "iframe", "url": "http://192.168.110.174:8603/m/economy/industry"}}]}, {"remote_id": 297, "title": "生活服务", "limit": 3, "column": 1, "bgUrl": "/szzy/h5/static/img/index/cj_src.png", "menus": [{"remote_id": 299, "icon": "iconrenwu1", "text": "电费缴纳", "subText": "查询缴纳", "color": "#2b35b2", "url": "/pages/under-develop/under-develop", "imgUri": "/static/img/shouye_icon/icon1.png", "bgUri": "/h5/static/img/index/<EMAIL>", "extra": {"title": "电费缴纳"}}, {"remote_id": 300, "icon": "iconrenwu1", "text": "燃气费查询缴纳", "subText": "查询缴纳", "color": "#2b35b2", "url": "/pages/under-develop/under-develop", "bgUri": "/h5/static/img/index/<EMAIL>", "imgUri": "/static/img/shouye_icon/icon2.png", "extra": {"title": "燃气费查询缴纳", "type": "iframe", "url": "http://192.168.110.174:8603/m/economy/epidemic"}}, {"remote_id": 301, "icon": "iconrenwu1", "text": "水费缴纳", "subText": "查询缴纳", "color": "#2b35b2", "url": "/pages/under-develop/under-develop", "bgUri": "/h5/static/img/index/<EMAIL>", "imgUri": "/static/img/shouye_icon/icon4.png", "extra": {"title": "水费缴纳", "type": "iframe", "url": "http://192.168.110.174:8603/m/economy/industry"}}]}, {"remote_id": 298, "title": "文旅服务", "limit": 3, "column": 1, "bgUrl": "/szzy/h5/static/img/index/ms_src.png", "menus": [{"remote_id": 299, "icon": "iconrenwu1", "text": "文博场馆", "subText": "场馆详情", "color": "#2b35b2", "url": "/pages/under-develop/under-develop", "imgUri": "/static/img/shouye_icon/icon1.png", "bgUri": "/h5/static/img/index/<EMAIL>", "extra": {"title": "文博场馆"}}, {"remote_id": 300, "icon": "iconrenwu1", "text": "来金华", "subText": "路线查询", "color": "#2b35b2", "url": "/pages/under-develop/under-develop", "bgUri": "/h5/static/img/index/<EMAIL>", "imgUri": "/static/img/shouye_icon/icon2.png", "extra": {"title": "来金华", "type": "iframe", "url": "http://192.168.110.174:8603/m/economy/epidemic"}}, {"remote_id": 301, "icon": "iconrenwu1", "text": "景区景点", "subText": "查看详情", "color": "#2b35b2", "url": "/pages/under-develop/under-develop", "bgUri": "/h5/static/img/index/<EMAIL>", "imgUri": "/static/img/shouye_icon/icon4.png", "extra": {"title": "景区景点", "type": "iframe", "url": "http://192.168.110.174:8603/m/economy/industry"}}]}, {"remote_id": 298, "title": "金融服务", "limit": 3, "column": 1, "bgUrl": "/szzy/h5/static/img/index/ms_src.png", "menus": [{"remote_id": 299, "icon": "iconrenwu1", "text": "上证指数", "subText": "了解详情", "color": "#2b35b2", "url": "/pages/under-develop/under-develop", "imgUri": "/static/img/shouye_icon/icon3.png", "bgUri": "/h5/static/img/index/<EMAIL>", "extra": {"title": "上证指数"}}, {"remote_id": 300, "icon": "iconrenwu1", "text": "深证指数", "subText": "了解详情", "color": "#2b35b2", "url": "/pages/under-develop/under-develop", "bgUri": "/h5/static/img/index/<EMAIL>", "imgUri": "/static/img/shouye_icon/icon3.png", "extra": {"title": "深证指数", "type": "iframe", "url": "http://192.168.110.174:8603/m/economy/epidemic"}}, {"remote_id": 301, "icon": "iconrenwu1", "text": "创业板指", "subText": "查看详情", "color": "#2b35b2", "url": "/pages/under-develop/under-develop", "bgUri": "/h5/static/img/index/<EMAIL>", "imgUri": "/static/img/shouye_icon/icon3.png", "extra": {"title": "创业板指", "type": "iframe", "url": "http://192.168.110.174:8603/m/economy/industry"}}]}, {"remote_id": 298, "title": "文教服务", "limit": 3, "column": 1, "bgUrl": "/szzy/h5/static/img/index/ms_src.png", "menus": [{"remote_id": 299, "icon": "iconrenwu1", "text": "学区查询", "subText": "了解详情", "color": "#2b35b2", "url": "/pages/under-develop/under-develop", "imgUri": "/static/img/shouye_icon/icon1.png", "bgUri": "/h5/static/img/index/<EMAIL>", "extra": {"title": "学区查询"}}, {"remote_id": 300, "icon": "iconrenwu1", "text": "入学登记", "subText": "登记入口", "color": "#2b35b2", "url": "/pages/under-develop/under-develop", "bgUri": "/h5/static/img/index/<EMAIL>", "imgUri": "/static/img/shouye_icon/icon2.png", "extra": {"title": "入学登记", "type": "iframe", "url": "http://192.168.110.174:8603/m/economy/epidemic"}}, {"remote_id": 301, "icon": "iconrenwu1", "text": "图书馆", "subText": "查看详情", "color": "#2b35b2", "url": "/pages/under-develop/under-develop", "bgUri": "/h5/static/img/index/<EMAIL>", "imgUri": "/static/img/shouye_icon/icon4.png", "extra": {"title": "图书馆", "type": "iframe", "url": "http://192.168.110.174:8603/m/economy/industry"}}]}, {"remote_id": 298, "title": "养老服务", "limit": 4, "column": 1, "bgUrl": "/szzy/h5/static/img/index/ms_src.png", "menus": [{"remote_id": 299, "icon": "iconrenwu1", "text": "养老待遇认证", "subText": "足不出户快捷办理", "color": "#2b35b2", "url": "/pages/under-develop/under-develop", "imgUri": "/static/img/shouye_icon/icon1.png", "bgUri": "/h5/static/img/index/<EMAIL>", "extra": {"title": "养老待遇认证"}}, {"remote_id": 300, "icon": "iconrenwu1", "text": "养老金领取", "subText": "足不出户快捷办理", "color": "#2b35b2", "url": "/pages/under-develop/under-develop", "bgUri": "/h5/static/img/index/<EMAIL>", "imgUri": "/static/img/shouye_icon/icon2.png", "extra": {"title": "养老金领取", "type": "iframe", "url": "http://192.168.110.174:8603/m/economy/epidemic"}}, {"remote_id": 301, "icon": "iconrenwu1", "text": "养老机构", "subText": "足不出户快捷办理", "color": "#2b35b2", "url": "/pages/under-develop/under-develop", "bgUri": "/h5/static/img/index/<EMAIL>", "imgUri": "/static/img/shouye_icon/icon4.png", "extra": {"title": "养老机构", "type": "iframe", "url": "http://192.168.110.174:8603/m/economy/industry"}}, {"remote_id": 301, "icon": "iconrenwu1", "text": "养老保险", "subText": "足不出户快捷办理", "color": "#2b35b2", "url": "/pages/under-develop/under-develop", "bgUri": "/h5/static/img/index/<EMAIL>", "imgUri": "/static/img/shouye_icon/icon4.png", "extra": {"title": "养老保险", "type": "iframe", "url": "http://192.168.110.174:8603/m/economy/industry"}}]}]}